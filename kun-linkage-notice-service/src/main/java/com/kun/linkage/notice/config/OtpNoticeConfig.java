package com.kun.linkage.notice.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "otp-notice")
public class OtpNoticeConfig {

    private String smsTemplateNo;

    private String emailTemplateNo;

    public String getSmsTemplateNo() {
        return smsTemplateNo;
    }

    public void setSmsTemplateNo(String smsTemplateNo) {
        this.smsTemplateNo = smsTemplateNo;
    }

    public String getEmailTemplateNo() {
        return emailTemplateNo;
    }

    public void setEmailTemplateNo(String emailTemplateNo) {
        this.emailTemplateNo = emailTemplateNo;
    }
}
