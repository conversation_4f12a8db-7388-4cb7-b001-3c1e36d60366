package com.kun.linkage.clearing.facade.api;

import com.kun.linkage.clearing.facade.vo.org.OrgClearingInquiryPageVO;
import com.kun.linkage.clearing.facade.vo.org.OrgClearingInquiryRequestVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "kun-linkage-clearing", path = "/linkage-clearing/org/clearing", configuration = FeignConfiguration.class)
public interface OrgClearingQueryFacade {

    /**
     * 分页查询清算数据
     *
     * @param requestVO 查询请求对象
     * @return 分页查询结果
     */
    @Operation(description = "分页查询清算数据", summary = "分页查询清算数据")
    @RequestMapping(value = "/pageList", method = RequestMethod.POST)
    Result<PageResult<OrgClearingInquiryPageVO>> pageList(@RequestBody OrgClearingInquiryRequestVO requestVO);

    /**
     * 异步导出清算数据
     *
     * @param requestVO 查询请求对象
     * @return 文件记录ID
     */
    @Operation(description = "异步导出清算数据", summary = "异步导出清算数据")
    @RequestMapping(value = "/asyncExport", method = RequestMethod.POST)
    Result<String> asyncExport(@RequestBody OrgClearingInquiryRequestVO requestVO);
}
