package com.kun.linkage.customer.controller.org;

import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ExportFileStatusEnum;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.customer.facade.api.bean.req.ExportFileRecordQueryVO;
import com.kun.linkage.customer.facade.api.bean.res.ExportFileRecordVO;
import com.kun.linkage.customer.service.export.ExportFileRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "ExportFileRecordController", description = "导出文件记录管理")
@RestController
@RequestMapping("/org/exportFileRecord")
public class ExportFileRecordController extends BaseVccBossController {

    @Resource
    private ExportFileRecordService exportFileRecordService;

    /**
     * 分页查询导出文件记录
     *
     * @param queryVO
     * @return
     */
    @Operation(description = "分页查询导出文件记录", summary = "分页查询导出文件记录")
    @PostMapping("/pageList")
    public Result<PageResult<ExportFileRecordVO>> pageList(@RequestBody ExportFileRecordQueryVO queryVO) {
        PageResult<ExportFileRecordVO> pageResult = exportFileRecordService.pageList(queryVO);
        return Result.success(pageResult);
    }

    /**
     * 获取文件下载URL（生成30分钟有效期的临时下载链接）
     *
     * @param fileRecordId 文件记录ID
     * @return 临时下载链接
     */
    @Operation(description = "获取文件下载URL", summary = "获取文件下载URL")
    @GetMapping("/downloadFile/{fileRecordId}")
    public Result<String> downloadFile(
            @Parameter(name = "fileRecordId", description = "文件记录ID", required = true)
            @PathVariable String fileRecordId) {

        // 参数验证
        if (StringUtils.isBlank(fileRecordId)) {
            return Result.fail(CommonTipConstant.REQUEST_PARAM_MISSING);
        }

        // 查询文件记录
        KLExportFileRecord fileRecord = exportFileRecordService.getById(fileRecordId);
        if (fileRecord == null) {
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }

        // 检查文件状态，只有成功状态的文件才能下载
        if (!ExportFileStatusEnum.SUCCESS.getCode().equals(fileRecord.getFileStatus())) {
            return Result.fail("文件未生成成功，无法下载");
        }

        // 检查 S3 URL 是否存在
        if (StringUtils.isBlank(fileRecord.getS3Url())) {
            return Result.fail("文件下载地址不存在");
        }

        try {
            // 生成带有30分钟有效期的临时下载链接（presigned URL）
            String presignedUrl = exportFileRecordService.generatePresignedUrl(fileRecord);
            return Result.success(presignedUrl);
        } catch (Exception e) {
            return Result.fail("生成下载链接失败: " + e.getMessage());
        }
    }
}
