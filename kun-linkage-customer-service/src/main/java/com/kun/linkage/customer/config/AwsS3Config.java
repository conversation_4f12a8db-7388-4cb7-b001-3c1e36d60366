package com.kun.linkage.customer.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.kun.common.util.aws.AwzS3Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * AWS S3 配置类
 * 用于配置 AmazonS3 客户端，支持 presigned URL 生成
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "kun.aws.s3.open", havingValue = "true")
public class AwsS3Config {

    @Resource
    private AwzS3Properties awzS3Properties;

    /**
     * 配置 AmazonS3 客户端
     * 支持自定义 endpoint 和认证信息
     *
     * @return AmazonS3 客户端实例
     */
    @Bean
    public AmazonS3 amazonS3() {
        log.info("初始化 AmazonS3 客户端，region: {}, bucket: {}",
                awzS3Properties.getRegion(), awzS3Properties.getBucket());

        AmazonS3ClientBuilder builder = AmazonS3ClientBuilder.standard();

        // 如果配置了自定义 endpoint
        if (StringUtils.isNotBlank(awzS3Properties.getEndpoint())) {
            log.info("使用自定义 S3 endpoint: {}", awzS3Properties.getEndpoint());
            builder.withEndpointConfiguration(
                    new AwsClientBuilder.EndpointConfiguration(
                            awzS3Properties.getEndpoint(),
                            awzS3Properties.getRegion()));

            // 当使用自定义 endpoint 时，强制使用 path-style 访问
            // 这可以避免 virtual-hosted-style 导致的 bucket 名称重复问题
            builder.withPathStyleAccessEnabled(true);
        } else {
            // 只有在没有自定义 endpoint 时才设置 region
            builder.withRegion(awzS3Properties.getRegion());
        }

        // 如果需要使用访问密钥
        if (awzS3Properties.isNeedSk() &&
                StringUtils.isNotBlank(awzS3Properties.getAccessKey()) &&
                StringUtils.isNotBlank(awzS3Properties.getSecretKey())) {

            BasicAWSCredentials awsCredentials = new BasicAWSCredentials(
                    awzS3Properties.getAccessKey(),
                    awzS3Properties.getSecretKey());
            builder.withCredentials(new AWSStaticCredentialsProvider(awsCredentials));
        }

        AmazonS3 amazonS3 = builder.build();

        log.info("AmazonS3 客户端初始化完成");
        return amazonS3;
    }
}
