package com.kun.linkage.customer.service.organization;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageParam;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.common.db.mapper.OrganizationBasicInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeDetailMapper;
import com.kun.linkage.customer.facade.api.bean.req.OrganizationFeeDetailPageQueryReq;
import com.kun.linkage.customer.facade.api.bean.res.OrganizationFeeDetailRes;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeDetailPageQueryDTO;
import com.kun.linkage.customer.facade.enums.DeductProcessorEnum;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构费用明细服务实现类
 * </p>
 *
 * @since 2025-07-25
 */
@Service
public class OrganizationFeeDetailService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationFeeDetailService.class);
    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;


    /**
     * 分页查询机构费用明细信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    public PageResult<OrganizationFeeDetailRes> pageList(OrganizationFeeDetailPageQueryReq req) {
        LocalDate now = LocalDate.now();
        if (req.getFeeEndDate().isAfter(now)) {
            log.warn("[手续费明细记录]结束时间不能超过当前,重置结束时间为当前时间");
            req.setFeeEndDate(now);
        }

        PageParam pageParam = new PageParam();
        pageParam.setPageNum(req.getPageNum());
        pageParam.setPageSize(req.getPageSize());

        PageResult<OrganizationFeeDetail> pageResult = PageHelperUtil.getPage(pageParam, () -> {
            LambdaQueryWrapper<OrganizationFeeDetail> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(OrganizationFeeDetail::getOrganizationNo, req.getOrganizationNo())
                    .between(OrganizationFeeDetail::getTransactionDatetime,
                            req.getFeeStartDate().atStartOfDay(),
                            req.getFeeEndDate().atTime(23, 59, 59));

            if (StringUtils.isNotBlank(req.getFeeType())) {
                queryWrapper.eq(OrganizationFeeDetail::getFeeType, req.getFeeType());
            }
            if (req.getRelatedTransactionId() != null) {
                queryWrapper.ge(OrganizationFeeDetail::getRelatedTransactionId, req.getRelatedTransactionId());
            }
            queryWrapper.orderByDesc(OrganizationFeeDetail::getCreateTime);

            return organizationFeeDetailMapper.selectList(queryWrapper);
        });

        List<OrganizationFeeDetailRes> organizationFeeDetailRes = pageResult.getData().stream()
                .map(detail -> {
                    OrganizationFeeDetailRes res = new OrganizationFeeDetailRes();
                    BeanUtils.copyProperties(detail, res);
                    return res;
                })
                .collect(Collectors.toList());
        return new PageResult(organizationFeeDetailRes, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotal(),
                pageResult.getExtraInfo());

    }

    /**
     * 分页查询机构费用明细信息(BOSS页面查询使用)
     *
     * @param req 查询条件
     * @return 分页结果
     */
    public PageResult<OrganizationFeeDetailVO> pageListByBOSS(OrganizationFeeDetailPageQueryDTO req) {
        LocalDate now = LocalDate.now();
        if (req.getFeeDateEnd().isAfter(now)) {
            log.warn("[手续费明细记录]结束时间不能超过当前,重置结束时间为当前时间");
            req.setFeeDateEnd(now);
        }
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, req.getOrganizationNo()));
        LambdaQueryWrapper<OrganizationFeeDetail> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrganizationFeeDetail::getOrganizationNo, req.getOrganizationNo())
                .between(OrganizationFeeDetail::getTransactionDatetime,
                        req.getFeeDateStart().atStartOfDay(),
                        req.getFeeDateEnd().atTime(23, 59, 59));
        if (StringUtils.isNotBlank(req.getFeeType())) {
            queryWrapper.eq(OrganizationFeeDetail::getFeeType, req.getFeeType());
        }
        if (StringUtils.isNotBlank(req.getRelatedTransactionId())) {
            queryWrapper.eq(OrganizationFeeDetail::getRelatedTransactionId, req.getRelatedTransactionId());
        }
        if (req.getFeeCollectionStatus() != null) {
            queryWrapper.eq(OrganizationFeeDetail::getFeeCollectionStatus, req.getFeeCollectionStatus());
        }
        queryWrapper.orderByDesc(OrganizationFeeDetail::getTransactionDatetime);
        PageResult<OrganizationFeeDetail> pageResult =
                PageHelperUtil.getPage(req, () -> organizationFeeDetailMapper.selectList(queryWrapper));
        List<OrganizationFeeDetailVO> details = Collections.emptyList();
        if (pageResult.getData() != null) {
            details = pageResult.getData().stream()
                    .map(detail -> {
                        OrganizationFeeDetailVO vo = new OrganizationFeeDetailVO();
                        vo.setId(detail.getId());
                        vo.setOrganizationNo(detail.getOrganizationNo());
                        if (organizationBasicInfo != null) {
                            vo.setOrganizationName(organizationBasicInfo.getOrganizationName());
                        }
                        vo.setFeeType(detail.getFeeType());
                        vo.setFeeCurrencyCode(detail.getTransactionCurrencyCode());
                        vo.setFeeAmount(detail.getFeeAmount());
                        vo.setFeeCollectionMethod(detail.getFeeCollectionMethod());
                        vo.setFeeDatetime(detail.getTransactionDatetime());
                        vo.setFeeCollectionStatus(detail.getFeeCollectionStatus());
                        if (StringUtils.equals(DeductProcessorEnum.KUN.getValue(), detail.getDeductProcessor())) {
                            vo.setKunCurrencyCode(detail.getDeductCurrencyCode());
                            vo.setKunAmount(detail.getDeductFeeAmount());
                        } else {
                            vo.setPayXAmount(detail.getDeductFeeAmount());
                            vo.setPayXCurrencyCode(detail.getDeductCurrencyCode());
                        }
                        vo.setRelatedTransactionId(detail.getRelatedTransactionId());
                        vo.setRemark(detail.getRemark());
                        return vo;
                    })
                    .collect(Collectors.toList());
        }
        return new PageResult<>(details, pageResult.getPageNum(), pageResult.getPageSize(),
                pageResult.getTotal(), pageResult.getExtraInfo());
    }
}
