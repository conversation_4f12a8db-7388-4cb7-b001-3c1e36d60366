package com.kun.linkage.customer.service.export;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.linkage.common.base.enums.ExportFileTypeEnum;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.common.db.mapper.OrganizationFeeDetailMapper;
import com.kun.linkage.customer.facade.api.bean.req.OrganizationFeeDetailPageQueryReq;
import com.kun.linkage.customer.facade.constant.ExportConstant;
import com.kun.linkage.customer.facade.enums.OrganizationFeeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 机构手续费明细异步导出服务
 */
@Slf4j
@Service
public class OrganizationFeeDetailExportService {

    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;

    @Resource
    private ExportFileRecordService exportFileRecordService;

    @Resource
    private AwsS3Util awsS3Util;

    @Resource
    private AwzS3Properties awzS3Properties;
    @Value("${kun.aws.s3.fileFolder:}")
    private String fileFolder;

    /**
     * 创建导出任务
     */
    public String createExportTask(OrganizationFeeDetailPageQueryReq req) {
        // 生成文件名
        String fileName = generateFileName(req.getOrganizationNo());

        // 创建文件记录
        KLExportFileRecord fileRecord = exportFileRecordService.createFileRecord(
                req.getOrganizationNo(),
                fileName,
                ExportFileTypeEnum.ORGANIZATION_FEE_EXPORT.getValue()
        );

        // 异步执行导出
        asyncExportData(fileRecord, req);

        return fileRecord.getFileRecordId();
    }

    /**
     * 异步导出手续费明细数据
     */
    @Async("externalApiAsyncExecutor")
    public void asyncExportData(KLExportFileRecord fileRecord, OrganizationFeeDetailPageQueryReq req) {
        String fileRecordId = fileRecord.getFileRecordId();
        try {
            log.info("开始异步导出手续费明细数据，文件记录ID: {}", fileRecordId);

            // 1. 查询数据
            List<OrganizationFeeDetail> dataList = queryExportData(req);
            log.info("查询到 {} 条手续费明细数据", dataList.size());

            // 2. 生成CSV文件
            File csvFile = generateCsvFile(dataList);
            log.info("CSV文件生成完成，文件大小: {} bytes", csvFile.length());

            // 3. 上传到S3
            String s3Url = uploadToS3(csvFile, fileRecord.getFileName());
            log.info("文件上传S3成功，URL: {}", s3Url);

            // 4. 更新文件记录为成功状态
            exportFileRecordService.updateFileRecordSuccess(fileRecordId, s3Url, csvFile.length());
            log.info("手续费明细导出任务完成，文件记录ID: {}", fileRecordId);

            // 5. 删除临时文件
            if (csvFile.exists()) {
                csvFile.delete();
                log.info("临时文件删除成功: {}", csvFile.getAbsolutePath());
            }

        } catch (Exception e) {
            log.error("手续费明细导出任务失败，文件记录ID: {}", fileRecordId, e);
            exportFileRecordService.updateFileRecordFailed(fileRecordId, e.getMessage());
        }
    }

    /**
     * 查询导出数据
     */
    private List<OrganizationFeeDetail> queryExportData(OrganizationFeeDetailPageQueryReq req) {
        LocalDate now = LocalDate.now();
        if (req.getFeeEndDate().isAfter(now)) {
            log.warn("[手续费明细记录]结束时间不能超过当前,重置结束时间为当前时间");
            req.setFeeEndDate(now);
        }

        LambdaQueryWrapper<OrganizationFeeDetail> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrganizationFeeDetail::getOrganizationNo, req.getOrganizationNo())
                .between(OrganizationFeeDetail::getTransactionDatetime,
                        req.getFeeStartDate().atStartOfDay(),
                        req.getFeeEndDate().atTime(23, 59, 59));

        if (StringUtils.isNotBlank(req.getFeeType())) {
            queryWrapper.eq(OrganizationFeeDetail::getFeeType, req.getFeeType());
        }
        if (StringUtils.isNotBlank(req.getRelatedTransactionId())) {
            queryWrapper.eq(OrganizationFeeDetail::getRelatedTransactionId, req.getRelatedTransactionId());
        }
        queryWrapper.orderByDesc(OrganizationFeeDetail::getCreateTime);

        return organizationFeeDetailMapper.selectList(queryWrapper);
    }

    /**
     * 生成CSV文件
     */
    private File generateCsvFile(List<OrganizationFeeDetail> dataList) throws IOException {
        File tempFile = File.createTempFile("fee_export_", ".csv");
        try (FileOutputStream fos = new FileOutputStream(tempFile);
             OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
             CSVPrinter csvPrinter = new CSVPrinter(osw, CSVFormat.DEFAULT.withHeader(ExportConstant.ORG_FEE_CSV_HEADERS))) {

            // 写入UTF-8 BOM头，确保中文正确显示
            fos.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            for (OrganizationFeeDetail detail : dataList) {
                csvPrinter.printRecord(
                        formatDateTime(detail.getTransactionDatetime()),     // Fee Date 日期
                        formatText(detail.getOrganizationNo()),              // 商户号
                        formatText(OrganizationFeeTypeEnum.getEnumByValue(detail.getFeeType()).getDesc()),  // Fee Type 手续费类型
                        formatText(detail.getTransactionCurrencyCode()),     // Fee Currency 手续费币种
                        formatAmount(detail.getFeeAmount()),                 // Fee Amount 手续费金额
                        formatText(detail.getDeductCurrencyCode()),          // Deduct Currency 扣收币种
                        formatAmount(detail.getDeductFeeAmount()),           // Deduct Amount 扣收金额
                        formatText(detail.getFeeCollectionMethod()),         // 收取方式
                        formatText(detail.getRelatedTransactionId()),        // 关联交易ID
                        formatText(detail.getRemark())                       // 备注
                );
            }
        }

        return tempFile;
    }

    /**
     * 上传文件到S3
     */
    private String uploadToS3(File file, String fileName) {
        String s3Path = fileFolder + "/" + ExportConstant.ORG_EXPORT_FILES_ROOT_DIR + "/" + ExportFileTypeEnum.ORGANIZATION_FEE_EXPORT.getFileDir();
        return awsS3Util.uploadChunkedFile(
                fileName,
                file,
                awzS3Properties.getBucket(),
                s3Path,
                CannedAccessControlList.PublicReadWrite
        );
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String organizationNo) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format(ExportFileTypeEnum.ORGANIZATION_FEE_EXPORT.getFileNameFormat(), organizationNo, timestamp);
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 格式化文本，处理null值
     */
    private String formatText(String text) {
        return text == null ? "" : ("\t" + text);
    }

    /**
     * 格式化金额
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        return amount.toPlainString();
    }

    /**
     * 验证日期范围
     */
    public void validateDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);
        if (daysBetween > ExportConstant.MAX_QUERY_DAYS) {
            throw new IllegalArgumentException(
                String.format("查询日期范围不能超过%d天，当前范围：%d天",
                    ExportConstant.MAX_QUERY_DAYS, daysBetween)
            );
        }
    }
}
