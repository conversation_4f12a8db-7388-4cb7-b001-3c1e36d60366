package com.kun.linkage.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageParam;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.CardRechargeDetail;
import com.kun.linkage.common.db.entity.OrganizationCustomerCardInfo;
import com.kun.linkage.common.db.mapper.CardRechargeDetailMapper;
import com.kun.linkage.common.db.mapper.OrganizationCustomerCardInfoMapper;
import com.kun.linkage.customer.facade.api.bean.req.OrgPageQueryCardRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.OrgPageQueryCardRechargeDetailRes;
import com.kun.linkage.customer.facade.enums.CardActiveStatusEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 卡充值业务服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class OrgCardRechargeQueryService {
    private static final Logger log = LoggerFactory.getLogger(OrgCardRechargeQueryService.class);
    @Resource
    private CardRechargeDetailMapper cardRechargeDetailMapper;

    /**
     * 分页查询卡充值记录
     *
     * @param req
     * @return
     */
    public PageResult<OrgPageQueryCardRechargeDetailRes> pageQueryCardRechargeDetail(OrgPageQueryCardRechargeDetailReq req) {
        if (req.getEndDate().isBefore(LocalDate.of(2025, 5, 1))) {
            log.warn("[分页查询卡充值记录]结束时间不能早于2025-05-01,直接返回空集合");
            return new PageResult<>(Collections.emptyList(), req.getPageNum(), req.getPageSize(), 0);
        }
        LocalDate now = LocalDate.now();
        if (req.getEndDate().isAfter(now)) {
            log.warn("[分页查询卡充值记录]结束时间不能超过当前,重置结束时间为当前时间");
            req.setEndDate(now);
        }
        long days = ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate());
        if (days > 365) {
            log.error("[分页查询卡充值记录]查询时间跨度不能超过365天");
            throw new BusinessException(CommonTipConstant.QUERY_TIME_SPAN_CANNOT_EXCEED_365_DAYS);
        }
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(req.getPageNum());
        pageParam.setPageSize(req.getPageSize());
        PageResult<CardRechargeDetail> pageResult = PageHelperUtil.getPage(pageParam, () -> {
            LambdaQueryWrapper<CardRechargeDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CardRechargeDetail::getOrganizationNo, req.getOrganizationNo())
                    .eq(CardRechargeDetail::getCustomerId, req.getCustomerId())
                    .ge(CardRechargeDetail::getRechargeDatetime, req.getStartDate().atTime(0, 0, 0))
                    .le(CardRechargeDetail::getRechargeDatetime, req.getEndDate().atTime(23, 59, 59))
                    .orderByDesc(CardRechargeDetail::getRechargeDatetime);

            if (StringUtils.isNotBlank(req.getCardId())) {
                queryWrapper.eq(CardRechargeDetail::getCardId, req.getCardId());
            }
            // 添加金额范围查询条件
            if (req.getRechargeAmountFrom() != null) {
                queryWrapper.ge(CardRechargeDetail::getRechargeAmount, req.getRechargeAmountFrom());
            }
            if (req.getRechargeAmountTo() != null) {
                queryWrapper.le(CardRechargeDetail::getRechargeAmount, req.getRechargeAmountTo());
            }
            // 添加状态查询条件
            if (StringUtils.isNotBlank(req.getRechargeStatus())) {
                queryWrapper.eq(CardRechargeDetail::getRechargeStatus, req.getRechargeStatus());
            }
            return cardRechargeDetailMapper.selectList(queryWrapper);
        });
        List<OrgPageQueryCardRechargeDetailRes> orgPageQueryCardRechargeDetailRes = pageResult.getData().stream()
                .map(detail -> {
                    OrgPageQueryCardRechargeDetailRes res = new OrgPageQueryCardRechargeDetailRes();
                    res.setCardId(detail.getCardId());
                    res.setRechargeDatetime(detail.getRechargeDatetime());
                    res.setRechargeAmount(detail.getRechargeAmount());
                    res.setRechargeCurrencyCode(detail.getRechargeCurrencyCode());
                    res.setRechargeCurrencyPrecision(detail.getRechargeCurrencyPrecision());
                    res.setDeductAmount(detail.getDeductTotalAmount());
                    res.setDeductCurrencyCode(detail.getDeductCurrencyCode());
                    res.setDeductCurrencyPrecision(detail.getDeductCurrencyPrecision());
                    res.setId(detail.getId());
                    res.setCustomerId(detail.getCustomerId());
                    res.setRechargeStatus(detail.getRechargeStatus());
                    res.setFailMessage(detail.getFailMessage());
                    res.setOrganizationNo(detail.getOrganizationNo());
                    res.setRequestNo(detail.getRequestNo());
                    return res;
                })
                .collect(Collectors.toList());
        return new PageResult(orgPageQueryCardRechargeDetailRes, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotal(),
                pageResult.getExtraInfo());

    }
}
