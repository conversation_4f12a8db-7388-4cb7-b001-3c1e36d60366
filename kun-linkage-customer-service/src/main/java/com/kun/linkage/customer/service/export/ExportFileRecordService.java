package com.kun.linkage.customer.service.export;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.common.base.enums.ExportFileStatusEnum;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.mapper.KLExportFileRecordMapper;
import com.kun.linkage.customer.facade.api.bean.req.ExportFileRecordQueryVO;
import com.kun.linkage.customer.facade.api.bean.res.ExportFileRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 导出文件记录服务
 */
@Slf4j
@Service
public class ExportFileRecordService {

    @Resource
    private KLExportFileRecordMapper klExportFileRecordMapper;

    /**
     * 创建文件记录
     */
    public KLExportFileRecord createFileRecord(String organizationNo, String fileName, String fileType) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setOrganizationNo(organizationNo);
        record.setFileName(fileName);
        record.setFileType(fileType);
        record.setFileStatus(ExportFileStatusEnum.PROCESSING.getCode());
        record.setCreateTime(DateTimeUtils.getCurrentDateTime());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.insert(record);
        log.info("创建文件记录成功，文件记录ID: {}, 文件名: {}", record.getFileRecordId(), fileName);
        return record;
    }

    /**
     * 更新文件记录为成功状态
     */
    public void updateFileRecordSuccess(String fileRecordId, String s3Url, long fileSize) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setS3Url(s3Url);
        record.setFileSize(fileSize);
        record.setFileStatus(ExportFileStatusEnum.SUCCESS.getCode());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.info("更新文件记录为成功状态，文件记录ID: {}, S3 URL: {}", fileRecordId, s3Url);
    }

    /**
     * 更新文件记录为失败状态
     */
    public void updateFileRecordFailed(String fileRecordId, String errorMessage) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setFileStatus(ExportFileStatusEnum.FAILED.getCode());
        record.setErrorMessage(errorMessage);
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.info("更新文件记录为失败状态，文件记录ID: {}, 错误信息: {}", fileRecordId, errorMessage);
    }

    /**
     * 根据ID查询文件记录
     */
    public KLExportFileRecord getById(String fileRecordId) {
        return klExportFileRecordMapper.selectById(fileRecordId);
    }

    /**
     * 分页查询文件记录
     */
    public PageResult<ExportFileRecordVO> pageList(ExportFileRecordQueryVO queryVO) {
        LambdaQueryWrapper<KLExportFileRecord> wrapper = Wrappers.lambdaQuery();

        if (StringUtils.isNotBlank(queryVO.getFileType())) {
            wrapper.eq(KLExportFileRecord::getFileType, queryVO.getFileType());
        }
        if (StringUtils.isNotBlank(queryVO.getFileStatus())) {
            wrapper.eq(KLExportFileRecord::getFileStatus, queryVO.getFileStatus());
        }
        if (StringUtils.isNotBlank(queryVO.getOrganizationNo())) {
            wrapper.eq(KLExportFileRecord::getOrganizationNo, queryVO.getOrganizationNo());
        }
        if (StringUtils.isNotBlank(queryVO.getCreateTimeFrom())) {
            wrapper.ge(KLExportFileRecord::getCreateTime, DateTimeUtils.truncateToSecond(
                    DateUtils.parseDate(queryVO.getCreateTimeFrom().trim() + " 00:00:00", DateUtils.DATETIME_PATTERN)));
        }
        if (StringUtils.isNotBlank(queryVO.getCreateTimeTo())) {
            wrapper.le(KLExportFileRecord::getCreateTime, DateTimeUtils.truncateToSecond(
                    DateUtils.parseDate(queryVO.getCreateTimeTo().trim() + " 23:59:59", DateUtils.DATETIME_PATTERN)));
        }

        wrapper.orderByDesc(KLExportFileRecord::getCreateTime);

        return PageHelperUtil.getPage(queryVO, () -> klExportFileRecordMapper.selectList(wrapper)
                .stream().map(record->{
                    ExportFileRecordVO vo = new ExportFileRecordVO();
                    BeanUtils.copyProperties(record, vo);
                    return vo;
                }).collect(Collectors.toList()));
    }
}
