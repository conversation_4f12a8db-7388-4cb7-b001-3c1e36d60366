package com.kun.linkage.customer.service.export;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.common.base.enums.ExportFileStatusEnum;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.mapper.KLExportFileRecordMapper;
import com.kun.linkage.customer.facade.api.bean.req.ExportFileRecordQueryVO;
import com.kun.linkage.customer.facade.api.bean.res.ExportFileRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 导出文件记录服务
 */
@Slf4j
@Service
public class ExportFileRecordService {

    @Resource
    private KLExportFileRecordMapper klExportFileRecordMapper;

    @Resource
    private AmazonS3 amazonS3;

    @Resource
    private AwzS3Properties awzS3Properties;

    /**
     * 创建文件记录
     */
    public KLExportFileRecord createFileRecord(String organizationNo, String fileName, String fileType) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setOrganizationNo(organizationNo);
        record.setFileName(fileName);
        record.setFileType(fileType);
        record.setFileStatus(ExportFileStatusEnum.PROCESSING.getCode());
        record.setCreateTime(DateTimeUtils.getCurrentDateTime());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.insert(record);
        log.info("创建文件记录成功，文件记录ID: {}, 文件名: {}", record.getFileRecordId(), fileName);
        return record;
    }

    /**
     * 更新文件记录为成功状态
     */
    public void updateFileRecordSuccess(String fileRecordId, String s3Url, long fileSize) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setS3Url(s3Url);
        record.setFileSize(fileSize);
        record.setFileStatus(ExportFileStatusEnum.SUCCESS.getCode());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.info("更新文件记录为成功状态，文件记录ID: {}, S3 URL: {}", fileRecordId, s3Url);
    }

    /**
     * 更新文件记录为失败状态
     */
    public void updateFileRecordFailed(String fileRecordId, String errorMessage) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setFileStatus(ExportFileStatusEnum.FAILED.getCode());
        record.setErrorMessage(errorMessage);
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.info("更新文件记录为失败状态，文件记录ID: {}, 错误信息: {}", fileRecordId, errorMessage);
    }

    /**
     * 根据ID查询文件记录
     */
    public KLExportFileRecord getById(String fileRecordId) {
        return klExportFileRecordMapper.selectById(fileRecordId);
    }

    /**
     * 分页查询文件记录
     */
    public PageResult<ExportFileRecordVO> pageList(ExportFileRecordQueryVO queryVO) {
        LambdaQueryWrapper<KLExportFileRecord> wrapper = Wrappers.lambdaQuery();

        if (StringUtils.isNotBlank(queryVO.getFileType())) {
            wrapper.eq(KLExportFileRecord::getFileType, queryVO.getFileType());
        }
        if (StringUtils.isNotBlank(queryVO.getFileStatus())) {
            wrapper.eq(KLExportFileRecord::getFileStatus, queryVO.getFileStatus());
        }
        if (StringUtils.isNotBlank(queryVO.getOrganizationNo())) {
            wrapper.eq(KLExportFileRecord::getOrganizationNo, queryVO.getOrganizationNo());
        }
        if (StringUtils.isNotBlank(queryVO.getCreateTimeFrom())) {
            wrapper.ge(KLExportFileRecord::getCreateTime, DateTimeUtils.truncateToSecond(
                    DateUtils.parseDate(queryVO.getCreateTimeFrom().trim() + " 00:00:00", DateUtils.DATETIME_PATTERN)));
        }
        if (StringUtils.isNotBlank(queryVO.getCreateTimeTo())) {
            wrapper.le(KLExportFileRecord::getCreateTime, DateTimeUtils.truncateToSecond(
                    DateUtils.parseDate(queryVO.getCreateTimeTo().trim() + " 23:59:59", DateUtils.DATETIME_PATTERN)));
        }

        wrapper.orderByDesc(KLExportFileRecord::getCreateTime);

        PageResult<KLExportFileRecord> pageResult = PageHelperUtil.getPage(queryVO, () -> klExportFileRecordMapper.selectList(wrapper));
        List<ExportFileRecordVO> fileRecordVOList = pageResult.getData().stream().map(record -> {
            ExportFileRecordVO vo = new ExportFileRecordVO();
            BeanUtils.copyProperties(record, vo);
            return vo;
        }).collect(Collectors.toList());
        return new PageResult(fileRecordVOList, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotal(),
                pageResult.getExtraInfo());
    }

    /**
     * 生成带有30分钟有效期的临时下载链接（presigned URL）
     *
     * @param fileRecord 文件记录
     * @return 临时下载链接
     */
    public String generatePresignedUrl(KLExportFileRecord fileRecord) {
        try {
            // 从 S3 URL 中提取文件 key
            String s3Url = fileRecord.getS3Url();
            String fileKey = extractFileKeyFromS3Url(s3Url);

            // 设置过期时间为30分钟
            Date expiration = new Date();
            long expTimeMillis = expiration.getTime();
            expTimeMillis += 1000 * 60 * 30; // 30分钟
            expiration.setTime(expTimeMillis);

            // 创建 presigned URL 请求
            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                new GeneratePresignedUrlRequest(awzS3Properties.getBucket(), fileKey)
                    .withMethod(HttpMethod.GET)
                    .withExpiration(expiration);

            // 生成 presigned URL
            URL presignedUrl = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);

            log.info("生成临时下载链接成功，文件key: {}, 有效期至: {}", fileKey, expiration);
            return presignedUrl.toString();

        } catch (Exception e) {
            log.error("生成临时下载链接失败，文件记录ID: {}, 错误信息: {}", fileRecord.getFileRecordId(), e.getMessage(), e);
            throw new RuntimeException("生成临时下载链接失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从 S3 URL 中提取文件 key
     *
     * @param s3Url S3 完整 URL
     * @return 文件 key
     */
    private String extractFileKeyFromS3Url(String s3Url) {
        if (StringUtils.isBlank(s3Url)) {
            throw new IllegalArgumentException("S3 URL 不能为空");
        }

        try {
            log.debug("提取文件key，S3 URL: {}, bucket: {}", s3Url, awzS3Properties.getBucket());

            if (!s3Url.startsWith("https://") && !s3Url.startsWith("http://")) {
                throw new IllegalArgumentException("S3 URL 格式不正确，必须以 http:// 或 https:// 开头");
            }

            // 移除协议部分
            String urlWithoutProtocol = s3Url.substring(s3Url.indexOf("://") + 3);

            // 查找第一个 "/" 的位置，这是域名和路径的分界点
            int firstSlashIndex = urlWithoutProtocol.indexOf("/");
            if (firstSlashIndex == -1) {
                throw new IllegalArgumentException("S3 URL 中没有找到文件路径");
            }

            // 提取路径部分（去掉域名）
            String fileKey = urlWithoutProtocol.substring(firstSlashIndex + 1);

            // 检查域名部分，判断 URL 格式
            String domainPart = urlWithoutProtocol.substring(0, firstSlashIndex);
            String bucketName = awzS3Properties.getBucket();

            if (domainPart.startsWith(bucketName + ".")) {
                // Virtual-hosted-style: https://bucket-name.s3.region.amazonaws.com/file-key
                // 路径部分就是文件 key
                log.debug("检测到 Virtual-hosted-style URL，文件key: {}", fileKey);
                return fileKey;
            } else {
                // Path-style: https://s3.region.amazonaws.com/bucket-name/file-key
                // 或 Custom endpoint: https://custom-endpoint.com/bucket-name/file-key
                // 需要从路径中移除 bucket 名称
                if (fileKey.startsWith(bucketName + "/")) {
                    String actualFileKey = fileKey.substring(bucketName.length() + 1);
                    log.debug("检测到 Path-style URL，移除bucket名称后的文件key: {}", actualFileKey);
                    return actualFileKey;
                } else {
                    // 如果路径不以 bucket 名称开头，可能整个路径就是文件 key
                    log.debug("使用完整路径作为文件key: {}", fileKey);
                    return fileKey;
                }
            }

        } catch (Exception e) {
            log.error("提取文件 key 失败，S3 URL: {}, 错误信息: {}", s3Url, e.getMessage());
            throw new IllegalArgumentException("提取文件 key 失败: " + e.getMessage(), e);
        }
    }
}
