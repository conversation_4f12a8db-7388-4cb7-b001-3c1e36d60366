package com.kun.linkage.common.base.enums;

public enum ExportFileTypeEnum {

    AUTHORIZATION_EXPORT("AUTHORIZATION_EXPORT", "authorizationFile", "Authorization_%s_%s.csv", "授权导出文件"),
    ORGANIZATION_FEE_EXPORT("ORGANIZATION_FEE_EXPORT", "orgFeeFile", "FeeDetail_%s_%s.csv", "机构手续费导出文件"),
    CLEARING_EXPORT("CLEARING_EXPORT", "clearingFile", "Settlement_%s_%s.csv", "清算导出文件"),
    CARD_RECHARGE_EXPORT("CARD_RECHARGE_EXPORT", "cardRechargeFile", "Topup_%s_%s.csv", "卡充值导出文件");

    private final String value;
    private final String fileDir;
    private final String fileNameFormat;
    private final String desc;


    ExportFileTypeEnum(String value, String fileDir, String fileNameFormat, String desc) {
        this.value = value;
        this.fileDir = fileDir;
        this.fileNameFormat = fileNameFormat;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getFileDir() {
        return fileDir;
    }

    public String getFileNameFormat() {
        return fileNameFormat;
    }

    public String getDesc() {
        return desc;
    }

    public static ExportFileTypeEnum getEnumByValue(String value) {
        for (ExportFileTypeEnum itemEnum : ExportFileTypeEnum.values()) {
            if (itemEnum.getValue().equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
}
