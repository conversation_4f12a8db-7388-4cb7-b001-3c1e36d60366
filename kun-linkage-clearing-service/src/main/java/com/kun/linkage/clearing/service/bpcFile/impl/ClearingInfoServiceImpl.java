package com.kun.linkage.clearing.service.bpcFile.impl;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.linkage.clearing.constant.ExportFileConstant;
import com.kun.linkage.clearing.enums.YesFlagEnum;
import com.kun.linkage.clearing.ext.mapper.ClearingInfoExtMapper;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageQueryResVO;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageQueryVO;
import com.kun.linkage.clearing.service.boss.IExportFileRecordService;
import com.kun.linkage.clearing.service.bpcFile.IClearingInfoService;
import com.kun.linkage.clearing.vo.KcAccountAssociationVo;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.ClearingInfo;
import com.kun.linkage.common.db.entity.KCExportFileRecord;
import com.kun.linkage.common.db.mapper.ClearingInfoMapper;
import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClearingInfoServiceImpl extends ServiceImpl<ClearingInfoMapper, ClearingInfo> implements IClearingInfoService {

    @Resource
    private ClearingInfoExtMapper clearingInfoExtMapper;
    @Resource
    private RedissonCacheUtil redissonCacheUtil;
    @Autowired
    private IExportFileRecordService exportFileRecordService;
    @Resource
    private AwsS3Util awsS3Util;
    @Resource
    private AwzS3Properties awzS3Properties;
    @Value("${kun.aws.s3.fileFolder:}")
    private String fileFolder;

    private static final String KC_ACCOUNT_ASSOCIATION_INFO = "kun-clearing:kc_account_association_info:";



    private static final LocalDate MIN_DATE = LocalDate.of(2025, 1, 1);

    @Override
    public Boolean updateRemainingClearAmountById(BigDecimal remainingClearAmount, Long clearingId,LocalDate clearingDate) {
        LambdaUpdateWrapper<ClearingInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ClearingInfo::getClearingId, clearingId)
                    .eq(ClearingInfo::getClearingDate, clearingDate)
                    .set(ClearingInfo::getRemainingClearAmount, remainingClearAmount)
                    .set(ClearingInfo::getReversalFlag, YesFlagEnum.YES.getNumValue())
                    .set(ClearingInfo::getUpdateTime,new Date());

        return this.update(updateWrapper);
    }

    @Override
    public Boolean updateNotifyResultsById(Long clearingId, LocalDate clearingDate,Integer notifyResults) {
        LambdaUpdateWrapper<ClearingInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ClearingInfo::getClearingId, clearingId)
                .eq(ClearingInfo::getClearingDate, clearingDate)
                .set(ClearingInfo::getNotifyResults, notifyResults)
                .set(ClearingInfo::getUpdateTime,new Date());
        return this.update(updateWrapper);
    }


    @Override
    public ClearingInfo selectOriginalClearingInfoByArn(String acqArn, String transCode, LocalDate startClearingDate, LocalDate endClearingDate) {
        LambdaQueryWrapper<ClearingInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClearingInfo::getAcqArn, acqArn)
                .eq(ClearingInfo::getDeleteFlag, YesFlagEnum.NO.getNumValue())
                .between(ClearingInfo::getClearingDate, startClearingDate, endClearingDate)
                .ne(ClearingInfo::getTransCode,transCode);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public Result<PageResult<KCClearingPageQueryResVO>> pageList(KCClearingPageQueryVO pageQueryVO) {
        LocalDate clearingStartDate = pageQueryVO.getClearingStartDate();
        LocalDate clearingEndDate = pageQueryVO.getClearingEndDate();
        if(null == clearingStartDate && null == clearingEndDate) {
            log.error("清分开始日期和清分结束日期必须要有一个");
            return Result.fail(CommonTipConstant.REQUEST_PARAM_MISSING);
        }

        if(null != clearingStartDate && clearingStartDate.isBefore(MIN_DATE)) {
            clearingStartDate = MIN_DATE;
        }

        if(null != clearingEndDate && clearingEndDate.isBefore(MIN_DATE)) {
            clearingEndDate = MIN_DATE;
        }


        LocalDate finalClearingStartDate = clearingStartDate;
        LocalDate finalClearingEndDate = clearingEndDate;
        PageResult<ClearingInfo> page = PageHelperUtil.getPage(pageQueryVO, () -> baseMapper.selectList(
                Wrappers.<ClearingInfo>lambdaQuery()
                        .ge(null != finalClearingStartDate, ClearingInfo::getClearingDate, finalClearingStartDate)
                        .le(null != finalClearingEndDate, ClearingInfo::getClearingDate, finalClearingEndDate)
                        .eq(StringUtils.isNotBlank(pageQueryVO.getChannelSource()), ClearingInfo::getChannelSource, pageQueryVO.getChannelSource())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getSystem()), ClearingInfo::getSystem, pageQueryVO.getSystem())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getClearingNo()), ClearingInfo::getClearingNo, pageQueryVO.getClearingNo())
                        .ge(StringUtils.isNotBlank(pageQueryVO.getAuthStartDate()), ClearingInfo::getAuthDate, pageQueryVO.getAuthStartDate())
                        .le(StringUtils.isNotBlank(pageQueryVO.getAuthEndDate()), ClearingInfo::getAuthDate, pageQueryVO.getAuthEndDate())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getOriginalClearingNo()), ClearingInfo::getOriginalClearingNo, pageQueryVO.getOriginalClearingNo())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getCustomerMerId()), ClearingInfo::getCustomerMerId, pageQueryVO.getCustomerMerId())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getReferenceNo()), ClearingInfo::getReferenceNo, pageQueryVO.getReferenceNo())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getTransactionType()), ClearingInfo::getTransactionType, pageQueryVO.getTransactionType())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getAcqArn()), ClearingInfo::getAcqArn, pageQueryVO.getAcqArn())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getKcardId()), ClearingInfo::getKcardId, pageQueryVO.getKcardId())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getAuthCode()), ClearingInfo::getAuthCode, pageQueryVO.getAuthCode())
                        .eq(StringUtils.isNotBlank(pageQueryVO.getClearingStatus()), ClearingInfo::getClearingStatus, pageQueryVO.getClearingStatus())
                        .eq(ClearingInfo::getDeleteFlag, YesFlagEnum.NO.getNumValue())
                        .orderByDesc(ClearingInfo::getCreateTime)));

        Map<String, String> orgInfoMap = selectOrgInfo();
        PageResult<KCClearingPageQueryResVO> result = convertToPageResult(page,orgInfoMap);
        return Result.success(result);

    }


    private PageResult<KCClearingPageQueryResVO> convertToPageResult(PageResult<ClearingInfo> page,Map<String, String> orgInfoMap) {
        List<KCClearingPageQueryResVO> list = page.getData().stream().map( clearingInfo ->{
            KCClearingPageQueryResVO pageQueryResVO = new KCClearingPageQueryResVO();
            pageQueryResVO.setClearingId(clearingInfo.getClearingId().toString());
            pageQueryResVO.setClearingDate(clearingInfo.getClearingDate());
            pageQueryResVO.setSystem(clearingInfo.getSystem());
            pageQueryResVO.setChannelSource(clearingInfo.getChannelSource());
            pageQueryResVO.setAuthDate(clearingInfo.getAuthDate());
            String customerMerId = clearingInfo.getCustomerMerId();
            pageQueryResVO.setCustomerMerId(customerMerId);
            if(null != orgInfoMap && !orgInfoMap.isEmpty() && StringUtils.isNotBlank(customerMerId)) {
                //设置机构名称
                pageQueryResVO.setCardAcceptorName(orgInfoMap.get(clearingInfo.getCustomerMerId()));
            }
            pageQueryResVO.setAcqMerchantName(clearingInfo.getCardAcceptorName());
            pageQueryResVO.setKcardId(clearingInfo.getKcardId());
            pageQueryResVO.setTransactionType(clearingInfo.getTransactionType());
            pageQueryResVO.setTransactionCurrencyCode(clearingInfo.getTransactionCurrencyCode());
            pageQueryResVO.setClearAmount(clearingInfo.getClearAmount().toString());
            pageQueryResVO.setCardholderCurrencyCode(clearingInfo.getCardholderCurrencyCode());
            pageQueryResVO.setCardholderAmount(clearingInfo.getCardholderAmount().toString());
            pageQueryResVO.setClearingStatus(clearingInfo.getClearingStatus());
            pageQueryResVO.setReferenceNo(clearingInfo.getReferenceNo());
            pageQueryResVO.setAuthCode(clearingInfo.getAuthCode());
            pageQueryResVO.setAcqArn(clearingInfo.getAcqArn());
            pageQueryResVO.setClearingNo(clearingInfo.getClearingNo());
            pageQueryResVO.setOriginalClearingNo(clearingInfo.getOriginalClearingNo());
            pageQueryResVO.setTransId(clearingInfo.getTransId());
            pageQueryResVO.setMaskedCardNumber(clearingInfo.getMaskedCardNo());
            pageQueryResVO.setSettlementFlag(clearingInfo.getSettlementFlag());
            pageQueryResVO.setIntechangeFeeSign(clearingInfo.getIntechangeFeeSign());
            pageQueryResVO.setIntechangeFeeAmt(clearingInfo.getIntechangeFeeAmt().toString());
            pageQueryResVO.setVisaCPDDate(clearingInfo.getCpd());
            return pageQueryResVO;
                }).collect(Collectors.toList());
        return new PageResult<KCClearingPageQueryResVO>(list , page.getPageNum(), page.getPageSize(),
                page.getTotal(), page.getExtraInfo());
    }

    @Override
    public Result<ClearingInfo> detail(String clearingId, LocalDate clearingDate) {
        LambdaQueryWrapper<ClearingInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClearingInfo::getClearingId, clearingId)
                .eq(ClearingInfo::getClearingDate, clearingDate);
        ClearingInfo clearingInfo = baseMapper.selectOne(queryWrapper);
        return Result.success(clearingInfo);
    }


    /**
     * 获取机构数据
     * @return
     */
    private Map<String,String> selectOrgInfo(){
        List<KcAccountAssociationVo> kcAccountAssociationVos = clearingInfoExtMapper.selectKcAccountAssociationName();

        if(redissonCacheUtil.exists(KC_ACCOUNT_ASSOCIATION_INFO)){
            return redissonCacheUtil.get(KC_ACCOUNT_ASSOCIATION_INFO);
        }

        Map<String,String> map = new HashMap<>();
        if(null != kcAccountAssociationVos && kcAccountAssociationVos.size()>0){
            map = kcAccountAssociationVos.stream()
                    .filter(vo -> vo.getAccountNo() != null)
                    .collect(Collectors.toMap(
                            KcAccountAssociationVo::getAccountNo,
                            vo -> vo.getAccountName() != null ? vo.getAccountName() : "",
                            (existing, replacement) -> replacement
                    ));
            redissonCacheUtil.set(KC_ACCOUNT_ASSOCIATION_INFO, map,2, TimeUnit.HOURS);
        }

        return map;
    }

    @Override
    public Result<String> asyncExport(KCClearingPageQueryVO pageQueryVO) {
        try {
            // 1. 参数验证
            Result<String> validationResult = validateExportParams(pageQueryVO);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 2. 创建文件记录
            String fileName = generateFileName();
            KCExportFileRecord fileRecord = exportFileRecordService.createFileRecord(fileName, ExportFileConstant.VISA_GW_SETTLEMENT_DATA_TYPE);

            // 3. 异步执行导出
            asyncExportData(fileRecord, pageQueryVO);

            return Result.success(fileRecord.getFileRecordId());
        } catch (Exception e) {
            log.error("异步导出清分数据失败", e);
            return Result.fail("导出任务创建失败：" + e.getMessage());
        }
    }

    /**
     * 验证导出参数
     */
    private Result<String> validateExportParams(KCClearingPageQueryVO pageQueryVO) {
        LocalDate clearingStartDate = pageQueryVO.getClearingStartDate();
        LocalDate clearingEndDate = pageQueryVO.getClearingEndDate();
        if (null == clearingStartDate && null == clearingEndDate) {
            log.error("清分开始日期和清分结束日期必须要有一个");
            return Result.fail(CommonTipConstant.REQUEST_PARAM_MISSING);
        }
        if (null == clearingStartDate) {
            clearingStartDate = clearingEndDate.minusDays(31);
        }

        if (null == clearingEndDate) {
            clearingEndDate = clearingStartDate.plusDays(31);
        }
        if (clearingStartDate.isBefore(MIN_DATE)) {
            clearingStartDate = MIN_DATE;
        }

        if (clearingEndDate.isBefore(MIN_DATE)) {
            clearingEndDate = MIN_DATE;
        }

        // 验证日期范围不超过31天
        long daysBetween = ChronoUnit.DAYS.between(clearingStartDate, clearingEndDate);
        if (daysBetween > ExportFileConstant.MAX_QUERY_DAYS) {
            return Result.fail("查询日期范围不能超过" + ExportFileConstant.MAX_QUERY_DAYS + "天");
        }
        pageQueryVO.setClearingStartDate(clearingStartDate);
        pageQueryVO.setClearingEndDate(clearingEndDate);
        return Result.success();
    }

    /**
     * 生成文件名
     */
    private String generateFileName() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format(ExportFileConstant.VISA_SETTLEMENT_DETAIL_FILE_TEMPLATE, timestamp);
    }

    /**
     * 异步执行数据导出
     */
    @Async("clearingTaskExecutor")
    public void asyncExportData(KCExportFileRecord fileRecord, KCClearingPageQueryVO pageQueryVO) {
        String fileRecordId = fileRecord.getFileRecordId();
        try {
            // 1. 查询数据
            List<ClearingInfo> dataList = queryExportData(pageQueryVO);
            log.info("查询到 {} 条清分数据", dataList.size());

            // 2. 生成CSV文件
            File csvFile = generateCsvFile(dataList);
            log.info("CSV文件生成完成，文件大小: {} bytes", csvFile.length());

            // 3. 上传到S3
            String s3Url = uploadToS3(csvFile, fileRecord.getFileName());
            log.info("文件上传S3成功，URL: {}", s3Url);

            // 4. 更新文件记录为成功状态
            exportFileRecordService.updateFileRecordSuccess(fileRecordId, s3Url, csvFile.length());

            // 5. 清理临时文件
            if (csvFile.exists()) {
                csvFile.delete();
            }

            log.info("异步导出清分数据完成，fileRecordId: {}", fileRecordId);
        } catch (Exception e) {
            log.error("异步导出清分数据失败，fileRecordId: {}", fileRecordId, e);
            exportFileRecordService.updateFileRecordFailed(fileRecordId, e.getMessage());
        }
    }

    /**
     * 查询导出数据
     */
    private List<ClearingInfo> queryExportData(KCClearingPageQueryVO pageQueryVO) {
        LocalDate clearingStartDate = pageQueryVO.getClearingStartDate();
        LocalDate clearingEndDate = pageQueryVO.getClearingEndDate();

        if (clearingStartDate != null && clearingStartDate.isBefore(MIN_DATE)) {
            clearingStartDate = MIN_DATE;
        }
        if (clearingEndDate != null && clearingEndDate.isBefore(MIN_DATE)) {
            clearingEndDate = MIN_DATE;
        }

        LambdaQueryWrapper<ClearingInfo> queryWrapper = Wrappers.<ClearingInfo>lambdaQuery()
                .ge(clearingStartDate != null, ClearingInfo::getClearingDate, clearingStartDate)
                .le(clearingEndDate != null, ClearingInfo::getClearingDate, clearingEndDate)
                .eq(StringUtils.isNotBlank(pageQueryVO.getChannelSource()), ClearingInfo::getChannelSource, pageQueryVO.getChannelSource())
                .eq(StringUtils.isNotBlank(pageQueryVO.getSystem()), ClearingInfo::getSystem, pageQueryVO.getSystem())
                .eq(StringUtils.isNotBlank(pageQueryVO.getClearingNo()), ClearingInfo::getClearingNo, pageQueryVO.getClearingNo())
                .ge(StringUtils.isNotBlank(pageQueryVO.getAuthStartDate()), ClearingInfo::getAuthDate, pageQueryVO.getAuthStartDate())
                .le(StringUtils.isNotBlank(pageQueryVO.getAuthEndDate()), ClearingInfo::getAuthDate, pageQueryVO.getAuthEndDate())
                .eq(StringUtils.isNotBlank(pageQueryVO.getOriginalClearingNo()), ClearingInfo::getOriginalClearingNo, pageQueryVO.getOriginalClearingNo())
                .eq(StringUtils.isNotBlank(pageQueryVO.getCustomerMerId()), ClearingInfo::getCustomerMerId, pageQueryVO.getCustomerMerId())
                .eq(StringUtils.isNotBlank(pageQueryVO.getReferenceNo()), ClearingInfo::getReferenceNo, pageQueryVO.getReferenceNo())
                .eq(StringUtils.isNotBlank(pageQueryVO.getTransactionType()), ClearingInfo::getTransactionType, pageQueryVO.getTransactionType())
                .eq(StringUtils.isNotBlank(pageQueryVO.getAcqArn()), ClearingInfo::getAcqArn, pageQueryVO.getAcqArn())
                .eq(StringUtils.isNotBlank(pageQueryVO.getKcardId()), ClearingInfo::getKcardId, pageQueryVO.getKcardId())
                .eq(StringUtils.isNotBlank(pageQueryVO.getAuthCode()), ClearingInfo::getAuthCode, pageQueryVO.getAuthCode())
                .eq(StringUtils.isNotBlank(pageQueryVO.getClearingStatus()), ClearingInfo::getClearingStatus, pageQueryVO.getClearingStatus())
                .eq(ClearingInfo::getDeleteFlag, YesFlagEnum.NO.getNumValue())
                .orderByDesc(ClearingInfo::getCreateTime);

        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 生成CSV文件
     */
    private File generateCsvFile(List<ClearingInfo> dataList) throws IOException {
        File tempFile = File.createTempFile("visa_settlement_detail_", ".csv");

        // 获取机构信息映射
        Map<String, String> orgInfoMap = selectOrgInfo();

        try (FileWriter fileWriter = new FileWriter(tempFile);
             CSVPrinter csvPrinter = new CSVPrinter(fileWriter, CSVFormat.DEFAULT.withHeader(ExportFileConstant.VISA_SETTLEMENT_DETAIL_HEADER))) {

            for (ClearingInfo clearingInfo : dataList) {
                // 获取商户名称
                String merchantName = orgInfoMap.getOrDefault(clearingInfo.getCustomerMerId(), "");

                csvPrinter.printRecord(
                    clearingInfo.getSystem(),                           // 业务系统
                    clearingInfo.getChannelSource(),                    // 通道
                    clearingInfo.getClearingDate(),                     // 清算日期
                    clearingInfo.getAuthDate(),                         // 授权日期时间
                    clearingInfo.getCustomerMerId(),                    // 商户号
                    merchantName,                                       // 商户名称
                    clearingInfo.getKcardId(),                         // Card ID
                    clearingInfo.getTransactionType(),                  // 交易类型
                    clearingInfo.getTransactionCurrencyCode(),          // 交易币种
                    clearingInfo.getClearAmount(),                      // 交易清算金额
                    clearingInfo.getCardholderCurrencyCode(),           // 持卡人币种
                    clearingInfo.getCardholderAmount(),                 // 持卡人清算金额
                    clearingInfo.getClearingStatus(),                   // 清算状态
                    clearingInfo.getReferenceNo(),                      // 参考号
                    clearingInfo.getAuthCode(),                         // 授权码
                    clearingInfo.getAcqArn(),                          // ARN
                    clearingInfo.getClearingNo(),                       // 清算流水号
                    clearingInfo.getOriginalClearingNo(),               // 原清算流水号
                    clearingInfo.getTransId()                           // 系统授权号
                );
            }
        }

        return tempFile;
    }

    /**
     * 上传文件到S3
     */
    private String uploadToS3(File file, String fileName) {
        return awsS3Util.uploadChunkedFile(
            fileName,
            file,
            awzS3Properties.getBucket(),
            fileFolder + ExportFileConstant.CLEARING_EXPORT_FOLDER,
            CannedAccessControlList.PublicReadWrite
        );
    }
}
