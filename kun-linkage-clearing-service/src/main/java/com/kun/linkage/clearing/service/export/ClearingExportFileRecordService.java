package com.kun.linkage.clearing.service.export;

import com.kun.linkage.common.base.enums.ExportFileStatusEnum;
import com.kun.linkage.common.base.enums.ExportFileTypeEnum;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.mapper.KLExportFileRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 清算导出文件记录服务
 */
@Slf4j
@Service
public class ClearingExportFileRecordService {

    @Resource
    private KLExportFileRecordMapper klExportFileRecordMapper;

    /**
     * 创建文件记录
     */
    public KLExportFileRecord createFileRecord(String organizationNo, String fileType) {
        String fileName = generateFileName(organizationNo);
        KLExportFileRecord record = new KLExportFileRecord();
        record.setOrganizationNo(organizationNo);
        record.setFileName(fileName);
        record.setFileType(fileType);
        record.setFileStatus(ExportFileStatusEnum.PROCESSING.getCode());
        record.setCreateTime(DateTimeUtils.getCurrentDateTime());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.insert(record);
        log.info("创建清算导出文件记录成功，文件记录ID: {}, 文件名: {}", record.getFileRecordId(), fileName);
        return record;
    }

    /**
     * 更新文件记录为成功状态
     */
    public void updateFileRecordSuccess(String fileRecordId, String s3Url, long fileSize) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setS3Url(s3Url);
        record.setFileSize(fileSize);
        record.setFileStatus(ExportFileStatusEnum.SUCCESS.getCode());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.info("更新清算导出文件记录为成功状态，文件记录ID: {}, S3 URL: {}", fileRecordId, s3Url);
    }

    /**
     * 更新文件记录为失败状态
     */
    public void updateFileRecordFailed(String fileRecordId, String errorMessage) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setFileStatus(ExportFileStatusEnum.FAILED.getCode());
        record.setErrorMessage(errorMessage);
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.error("更新清算导出文件记录为失败状态，文件记录ID: {}, 错误信息: {}", fileRecordId, errorMessage);
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String organizationNo) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format(ExportFileTypeEnum.CLEARING_EXPORT.getFileNameFormat(), organizationNo, timestamp);
    }

}
