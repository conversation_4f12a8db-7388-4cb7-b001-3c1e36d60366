package com.kun.linkage.clearing.service.export;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.clearing.enums.ClearIngTypeEnum;
import com.kun.linkage.clearing.facade.constant.ClearingExportConstant;
import com.kun.linkage.clearing.facade.vo.org.OrgClearingInquiryRequestVO;
import com.kun.linkage.common.base.enums.ExportFileTypeEnum;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLClearingTrans;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.mapper.KLClearingTransMapper;
import com.kun.linkage.customer.facade.constant.ExportConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 清算数据异步导出服务
 */
@Slf4j
@Service
public class ClearingDataExportService {

    @Resource
    private KLClearingTransMapper clearingTransMapper;

    @Resource
    private ClearingExportFileRecordService exportFileRecordService;

    @Resource
    private AwsS3Util awsS3Util;

    @Resource
    private AwzS3Properties awzS3Properties;

    @Value("${file.folder:clearing}")
    private String fileFolder;

    /**
     * 异步导出清算记录数据
     */
    @Async("clearingTaskExecutor")
    public void asyncExportData(KLExportFileRecord fileRecord, OrgClearingInquiryRequestVO requestVO) {
        String fileRecordId = fileRecord.getFileRecordId();
        try {
            log.info("开始异步导出清算记录数据，文件记录ID: {}", fileRecordId);

            // 1. 查询数据
            List<KLClearingTrans> dataList = queryExportData(requestVO);
            log.info("查询到 {} 条清算记录数据", dataList.size());

            // 2. 生成CSV文件
            File csvFile = generateCsvFile(dataList);
            log.info("CSV文件生成完成，文件大小: {} bytes", csvFile.length());

            // 3. 上传到S3
            String s3Url = uploadToS3(csvFile, fileRecord.getFileName());
            log.info("文件上传S3成功，URL: {}", s3Url);

            // 4. 更新文件记录为成功状态
            exportFileRecordService.updateFileRecordSuccess(fileRecordId, s3Url, csvFile.length());

            // 5. 清理临时文件
            if (csvFile.exists()) {
                csvFile.delete();
                log.info("临时文件已清理: {}", csvFile.getAbsolutePath());
            }
            log.info("清算记录数据导出完成，文件记录ID: {}", fileRecordId);
        } catch (Exception e) {
            log.error("清算记录数据导出失败，文件记录ID: {}, 错误: {}", fileRecordId, e.getMessage(), e);
            exportFileRecordService.updateFileRecordFailed(fileRecordId, e.getMessage());
        }
    }

    /**
     * 查询导出数据
     */
    private List<KLClearingTrans> queryExportData(OrgClearingInquiryRequestVO requestVO) {
        LambdaQueryWrapper<KLClearingTrans> wrapper = buildQueryWrapper(requestVO);
        return clearingTransMapper.selectList(wrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<KLClearingTrans> buildQueryWrapper(OrgClearingInquiryRequestVO requestVO) {
        LambdaQueryWrapper<KLClearingTrans> wrapper = Wrappers.lambdaQuery();

        // 清算日期范围（必填）
        wrapper.ge(KLClearingTrans::getCreateTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getClearingDateFrom().trim() + " 00:00:00", DateUtils.DATETIME_PATTERN)));
        wrapper.le(KLClearingTrans::getCreateTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getClearingDateUntil().trim() + " 23:59:59", DateUtils.DATETIME_PATTERN)));

        // 其他查询条件
        if (requestVO.getClearingId() != null) {
            wrapper.eq(KLClearingTrans::getClearingId, requestVO.getClearingId());
        }
        if (requestVO.getProcessor() != null) {
            wrapper.eq(KLClearingTrans::getProcessor, requestVO.getProcessor());
        }
        if (requestVO.getAuthorizationDateFrom() != null) {
            wrapper.ge(KLClearingTrans::getTransTime, DateTimeUtils.truncateToSecond(
                    DateUtils.parseDate(requestVO.getAuthorizationDateFrom().trim() + " 00:00:00", DateUtils.DATETIME_PATTERN)));
        }
        if (requestVO.getAuthorizationDateUntil() != null) {
            wrapper.le(KLClearingTrans::getTransTime, DateTimeUtils.truncateToSecond(
                    DateUtils.parseDate(requestVO.getAuthorizationDateUntil() + " 23:59:59", DateUtils.DATETIME_PATTERN)));
        }
        if (requestVO.getMerchantNo() != null) {
            wrapper.eq(KLClearingTrans::getMerchantNo, requestVO.getMerchantNo());
        }
        if (requestVO.getTransType() != null) {
            wrapper.eq(KLClearingTrans::getTransType, requestVO.getTransType());
        }
        if (StringUtils.isNotBlank(requestVO.getAcquireReferenceNo())) {
            wrapper.eq(KLClearingTrans::getAcquireReferenceNo, requestVO.getAcquireReferenceNo());
        }
        if (StringUtils.isNotBlank(requestVO.getArn())) {
            wrapper.eq(KLClearingTrans::getArn, requestVO.getArn());
        }
        if (StringUtils.isNotBlank(requestVO.getGatewayCardId())) {
            wrapper.eq(KLClearingTrans::getGatewayCardId, requestVO.getGatewayCardId());
        }
        if (StringUtils.isNotBlank(requestVO.getApproveCode())) {
            wrapper.eq(KLClearingTrans::getApproveCode, requestVO.getApproveCode());
        }
        if (StringUtils.isNotBlank(requestVO.getClearingStatus())) {
            wrapper.eq(KLClearingTrans::getStatus, requestVO.getClearingStatus());
        }
        if (StringUtils.isNotBlank(requestVO.getCardNoSuffix())) {
            wrapper.eq(KLClearingTrans::getMaskedCardNo, requestVO.getCardNoSuffix());
        }
        if (StringUtils.isNotBlank(requestVO.getClearingType())) {
            wrapper.eq(KLClearingTrans::getClearingType, requestVO.getClearingType());
        }
        if (StringUtils.isNotBlank(requestVO.getGatewayRequestId())) {
            wrapper.eq(KLClearingTrans::getGatewayRequestId, requestVO.getGatewayRequestId());
        }
        if (StringUtils.isNotBlank(requestVO.getGatewayClearingId())) {
            wrapper.eq(KLClearingTrans::getGatewayClearingId, requestVO.getGatewayClearingId());
        }
        if (StringUtils.isNotBlank(requestVO.getOriginalGatewayClearingId())) {
            wrapper.eq(KLClearingTrans::getOriginalGatewayClearingId, requestVO.getOriginalGatewayClearingId());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthFlowId())) {
            wrapper.eq(KLClearingTrans::getAuthFlowId, requestVO.getAuthFlowId());
        }

        wrapper.orderByDesc(KLClearingTrans::getCreateTime);
        return wrapper;
    }

    /**
     * 生成CSV文件
     */
    private File generateCsvFile(List<KLClearingTrans> dataList) throws IOException {
        File tempFile = File.createTempFile("clearing_export_", ".csv");

        try (FileOutputStream fos = new FileOutputStream(tempFile);
             OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
             CSVPrinter csvPrinter = new CSVPrinter(osw, CSVFormat.DEFAULT.withHeader(ClearingExportConstant.CSV_HEADERS))) {

            // 写入UTF-8 BOM头，确保中文正确显示
            fos.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            // 写入数据行
            for (KLClearingTrans clearingTrans : dataList) {
                csvPrinter.printRecord(
                        formatDate(clearingTrans.getCreateTime()),                    // 清算日期
                        formatText(clearingTrans.getClearingId()),                    // 清算流水号
                        formatText(clearingTrans.getMerchantNo()),                    // 商户号
                        formatText(clearingTrans.getGatewayCardId()),                 // Card ID
                        ClearIngTypeEnum.getEnumByClearType(clearingTrans.getClearingType()),                              // 清算类型
                        clearingTrans.getTransCurrency(),                             // 交易币种
                        formatAmount(clearingTrans.getTransAmount()),                 // 交易金额
                        clearingTrans.getCardholderBillingCurrency(),                 // 持卡人币种
                        formatAmount(clearingTrans.getCardholderBillingAmount()),     // 持卡人金额
                        clearingTrans.getStatus(),                                    // 清算状态
                        formatText(clearingTrans.getAcquireReferenceNo()),            // 参考号
                        formatText(clearingTrans.getApproveCode()),                               // 授权码
                        formatText(clearingTrans.getAuthFlowId()),                    // 原系统授权号
                        clearingTrans.getCardAcceptorName(),                          // 收单商户名称
                        formatText(clearingTrans.getMcc()),                                       // 收单商户MCC
                        formatText(clearingTrans.getOriginalClearingId())             // 原清算流水号
                );
            }
        }

        return tempFile;
    }

    /**
     * 上传文件到S3
     */
    private String uploadToS3(File file, String fileName) {
        String s3Path = fileFolder + "/" + ExportConstant.ORG_EXPORT_FILES_ROOT_DIR + "/" + ExportFileTypeEnum.CLEARING_EXPORT.getFileDir();
        return awsS3Util.uploadChunkedFile(
                fileName,
                file,
                awzS3Properties.getBucket(),
                s3Path,
                CannedAccessControlList.PublicReadWrite
        );
    }

    /**
     * 格式化可能被识别为数字的字段，防止Excel显示为科学计数法
     */
    private String formatText(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        // 在值前添加制表符，强制Excel将其作为文本处理
        return "\t" + value;
    }

    /**
     * 格式化日期
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return DateUtils.formatDate(date, DateUtils.DAY_PATTERN);
    }

    /**
     * 格式化金额
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "";
        }
        return amount.toString();
    }

    /**
     * 验证日期范围
     */
    public void validateDateRange(String startDate, String endDate) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        try {
            Date start = DateUtils.parseDate(startDate, DateUtils.DAY_PATTERN);
            Date end = DateUtils.parseDate(endDate, DateUtils.DAY_PATTERN);

            if (start.after(end)) {
                throw new IllegalArgumentException("开始日期不能晚于结束日期");
            }

            long daysBetween = (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000);
            if (daysBetween > ClearingExportConstant.MAX_QUERY_DAYS) {
                throw new IllegalArgumentException(
                        String.format("查询日期范围不能超过%d天，当前范围：%d天",
                                ClearingExportConstant.MAX_QUERY_DAYS, daysBetween)
                );
            }
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                throw e;
            }
            throw new IllegalArgumentException("日期格式错误，请使用yyyy-MM-dd格式");
        }
    }
}
