package com.kun.linkage.clearing.controller.org;

import com.kun.linkage.clearing.facade.constant.ClearingApplicationRequestParamNameConstant;
import com.kun.linkage.clearing.facade.constant.KunLinkageClearingResponseCodeEnum;
import com.kun.linkage.clearing.facade.vo.org.OrgClearingInquiryPageVO;
import com.kun.linkage.clearing.facade.vo.org.OrgClearingInquiryRequestVO;
import com.kun.linkage.clearing.service.export.ClearingDataExportService;
import com.kun.linkage.clearing.service.export.ClearingExportFileRecordService;
import com.kun.linkage.clearing.service.kunlinkage.OrgKLClearingTransService;
import com.kun.linkage.clearing.utils.I18nMessageService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.enums.ExportFileTypeEnum;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "OrgKLClearingQueryController", description = "商户端清算数据查询")
@RestController
@RequestMapping("/org/clearing")
public class OrgKLClearingQueryController {

    @Resource
    private I18nMessageService i18nMessageService;

    @Resource
    private OrgKLClearingTransService clearingTransService;

    @Resource
    private ClearingDataExportService clearingDataExportService;

    @Resource
    private ClearingExportFileRecordService exportFileRecordService;

    /**
     * 分页查询清算数据
     *
     * @param requestVO 查询请求对象
     * @return 分页查询结果
     */
    @Operation(description = "分页查询清算数据", summary = "分页查询清算数据")
    @PostMapping("/pageList")
    public Result<PageResult<OrgClearingInquiryPageVO>> pageList(@RequestBody OrgClearingInquiryRequestVO requestVO) {
        if (StringUtils.isBlank(requestVO.getClearingDateFrom())) {
            return Result.fail(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(ClearingApplicationRequestParamNameConstant.CLEARING_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getClearingDateUntil())) {
            return Result.fail(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(ClearingApplicationRequestParamNameConstant.CLEARING_DATE_TO)));
        }
        PageResult<OrgClearingInquiryPageVO> pageList = clearingTransService.pageList(requestVO);
        return Result.success(pageList);
    }

    /**
     * 异步导出清算数据
     *
     * @param requestVO 查询请求对象
     * @return 文件记录ID
     */
    @Operation(description = "异步导出清算数据", summary = "异步导出清算数据")
    @PostMapping("/asyncExport")
    public Result<String> asyncExport(@RequestBody OrgClearingInquiryRequestVO requestVO) {
        // 1. 参数校验
        if (StringUtils.isBlank(requestVO.getClearingDateFrom())) {
            return Result.fail(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(ClearingApplicationRequestParamNameConstant.CLEARING_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getClearingDateUntil())) {
            return Result.fail(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(ClearingApplicationRequestParamNameConstant.CLEARING_DATE_TO)));
        }

        // 2. 日期范围验证（不超过31天）
        try {
            clearingDataExportService.validateDateRange(requestVO.getClearingDateFrom(), requestVO.getClearingDateUntil());
        } catch (IllegalArgumentException e) {
            return Result.fail(KunLinkageClearingResponseCodeEnum.UNKNOWN_ERROR.getCode(), e.getMessage());
        }
        // 4. 创建文件记录
        KLExportFileRecord fileRecord = exportFileRecordService.createFileRecord(requestVO.getMerchantNo(), ExportFileTypeEnum.CLEARING_EXPORT.getValue());

        // 5. 启动异步导出任务
        clearingDataExportService.asyncExportData(fileRecord, requestVO);

        // 6. 立即返回文件记录ID
        return Result.success(fileRecord.getFileRecordId());
    }

}
