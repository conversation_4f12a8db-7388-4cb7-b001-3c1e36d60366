package com.kun.linkage.clearing.enums;

public enum CardholderBillingRateSourceEnum {
    DEFAULT("DEFAULT","默认"),
    PAY_X("PAY_X","payX"),
    AUTH_FLOW("AUTH_FLOW","授权流水");

    private final String value;
    private final String description;

    public String getValue() {
        return value;
    }


    public String getDescription() {
        return description;
    }

    CardholderBillingRateSourceEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
