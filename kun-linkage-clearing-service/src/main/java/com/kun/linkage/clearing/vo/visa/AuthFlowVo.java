package com.kun.linkage.clearing.vo.visa;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ø
 * <p>
 * 卡核心回调通知记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
public class AuthFlowVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键 id
     */
    private Long id;


    /**
     * 请求流水号
     */
    private String requestId;


    /**
     * 交易类型
     */
    private Integer transType;


    /**
     * 交易ID
     */
    private String transId;

    /**
     * 原交易ID
     */
    private String originalTransId;

    /**
     * 原交易完成时间
     */
    private Date originalFinishTime;


    /**
     * 检索参考号
     */
    private String retrievalReferenceNumber;

    /**
     * 持卡人账单货币代码
     */
    private String currencyCodeCardholderBilling;

    /**
     * 交易货币代码
     */
    private String currencyCodeTransaction;

    /**
     * 持卡人账单汇率
     */
    private String conversionRateCardholderBilling;

    /**
     * 系统跟踪审计号
     */
    private String systemsTraceAuditNumber;


    /**
     * FE交易号
     */
    private String feTransactionNumber;


    /**
     * 受卡方识别码
     */
    private String cardAcceptorIdentificationCode;


    /**
     * 本地交易日期时间
     */
    private String dateTimeLocalTransaction;


    /**
     * 银行商户名
     */
    private String cardAcceptorName;


    /**
     * 国家代码
     */
    private String countryCode;


    /**
     * 终端标识
     */
    private String cardAcceptorTerminalIdentification;

    /**
     * 授权类型 普通授权 预授权
     */
    private String authType;

    /**
     * markup比例
     */
    private BigDecimal markupRate;

    /**
     * 交易总金额 不含markup金额 不会变动
     */
    private BigDecimal transAmt;

    /**
     * 交易币种金额汇总  在授权、授权追加时增加，撤销、退款时减少，在撤销、退款时，判断交易币种金额是否超出，若超出，直接返回失败
     */
    private BigDecimal remainAuthAmt;

    /**
     * 账单金额汇总（不含markup），在授权、授权追加啊时增加，撤销、清算时减少，（清算时比对这个金额是否超额清算）。退款时不受影响
     */
    private BigDecimal remainBillAmt;

    /**
     * 剩余账单总金额（含markup）在授权、授权追加时增加，撤销、退款时减少，在撤销、退款时，若账单金额大于这个金额，则取表里这个金额进行撤销或者退款
     */
    private BigDecimal remainBillAmtWithMarkup;

    /**
     * 剩余冻结金额 含markup的账单金额汇总，在授权、授权追加增加，撤销、清算时减少，这个字段用于在清算之后进行授权到期释放
     */
    private BigDecimal remainFrozenAmt;

    /**
     * 返回信息
     */
    private String returnMessage;

    /**
     * 授权码
     */
    private String approveCode;

    /**
     * /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 表中没有的数据,自己用
     */
    private String tableName;

    /**
     * 表里面没有的数据,自己用(第一笔匹配上的RequestId)
     */
    private String originalProcessorRequestId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }


    public Integer getTransType() {
        return transType;
    }

    public void setTransType(Integer transType) {
        this.transType = transType;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getOriginalTransId() {
        return originalTransId;
    }

    public void setOriginalTransId(String originalTransId) {
        this.originalTransId = originalTransId;
    }

    public Date getOriginalFinishTime() {
        return originalFinishTime;
    }

    public void setOriginalFinishTime(Date originalFinishTime) {
        this.originalFinishTime = originalFinishTime;
    }


    public String getRetrievalReferenceNumber() {
        return retrievalReferenceNumber;
    }

    public void setRetrievalReferenceNumber(String retrievalReferenceNumber) {
        this.retrievalReferenceNumber = retrievalReferenceNumber;
    }

    public String getCurrencyCodeCardholderBilling() {
        return currencyCodeCardholderBilling;
    }

    public void setCurrencyCodeCardholderBilling(String currencyCodeCardholderBilling) {
        this.currencyCodeCardholderBilling = currencyCodeCardholderBilling;
    }

    public String getCurrencyCodeTransaction() {
        return currencyCodeTransaction;
    }

    public void setCurrencyCodeTransaction(String currencyCodeTransaction) {
        this.currencyCodeTransaction = currencyCodeTransaction;
    }

    public String getConversionRateCardholderBilling() {
        return conversionRateCardholderBilling;
    }

    public void setConversionRateCardholderBilling(String conversionRateCardholderBilling) {
        this.conversionRateCardholderBilling = conversionRateCardholderBilling;
    }

    public String getSystemsTraceAuditNumber() {
        return systemsTraceAuditNumber;
    }

    public void setSystemsTraceAuditNumber(String systemsTraceAuditNumber) {
        this.systemsTraceAuditNumber = systemsTraceAuditNumber;
    }


    public String getFeTransactionNumber() {
        return feTransactionNumber;
    }

    public void setFeTransactionNumber(String feTransactionNumber) {
        this.feTransactionNumber = feTransactionNumber;
    }


    public String getCardAcceptorIdentificationCode() {
        return cardAcceptorIdentificationCode;
    }

    public void setCardAcceptorIdentificationCode(String cardAcceptorIdentificationCode) {
        this.cardAcceptorIdentificationCode = cardAcceptorIdentificationCode;
    }


    public String getDateTimeLocalTransaction() {
        return dateTimeLocalTransaction;
    }

    public void setDateTimeLocalTransaction(String dateTimeLocalTransaction) {
        this.dateTimeLocalTransaction = dateTimeLocalTransaction;
    }


    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }


    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCardAcceptorTerminalIdentification() {
        return cardAcceptorTerminalIdentification;
    }

    public void setCardAcceptorTerminalIdentification(String cardAcceptorTerminalIdentification) {
        this.cardAcceptorTerminalIdentification = cardAcceptorTerminalIdentification;
    }


    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }


    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }

    public BigDecimal getTransAmt() {
        return transAmt;
    }

    public void setTransAmt(BigDecimal transAmt) {
        this.transAmt = transAmt;
    }

    public BigDecimal getRemainAuthAmt() {
        return remainAuthAmt;
    }

    public void setRemainAuthAmt(BigDecimal remainAuthAmt) {
        this.remainAuthAmt = remainAuthAmt;
    }

    public BigDecimal getRemainBillAmt() {
        return remainBillAmt;
    }

    public void setRemainBillAmt(BigDecimal remainBillAmt) {
        this.remainBillAmt = remainBillAmt;
    }

    public BigDecimal getRemainBillAmtWithMarkup() {
        return remainBillAmtWithMarkup;
    }

    public void setRemainBillAmtWithMarkup(BigDecimal remainBillAmtWithMarkup) {
        this.remainBillAmtWithMarkup = remainBillAmtWithMarkup;
    }

    public BigDecimal getRemainFrozenAmt() {
        return remainFrozenAmt;
    }

    public void setRemainFrozenAmt(BigDecimal remainFrozenAmt) {
        this.remainFrozenAmt = remainFrozenAmt;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public String getApproveCode() {
        return approveCode;
    }

    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getOriginalProcessorRequestId() {
        return originalProcessorRequestId;
    }

    public void setOriginalProcessorRequestId(String originalProcessorRequestId) {
        this.originalProcessorRequestId = originalProcessorRequestId;
    }
}
