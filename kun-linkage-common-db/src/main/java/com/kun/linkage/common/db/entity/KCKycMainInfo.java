package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * KYC 商户基础信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@TableName("kc_kyc_main_info")
public class KCKycMainInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员编号 id
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 上级代理会员 id
     */
    @TableField("super_member_id")
    private Long superMemberId;

    /**
     * 资料补充步骤（1、2、3、4、5）
     */
    @TableField("step")
    private Integer step;

    /**
     * 注册邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 入驻类型
     */
    @TableField("settled_type")
    private String settledType;

    /**
     * 开设账户类型
     */
    @TableField("open_account_type")
    private String openAccountType;

    /**
     * 审核流水号
     */
    @TableField("audit_seq_no")
    private String auditSeqNo;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态（初始化、待审核、审核通过、审核拒绝）
     */
    @TableField("audit_status")
    private String auditStatus;

    /**
     * 企业名称（英文）
     */
    @TableField("english_name")
    private String englishName;

    /**
     * 商户状态（待激活、已激活、已冻结）
     */
    @TableField("status")
    private String status;

    /**
     * 商户自定义卡面
     */
    @TableField("card_face")
    private String cardFace;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * API状态 ENABLED-开通 DISABLED-关闭
     */
    @TableField("api_status")
    private String apiStatus;

    /**
     * 解密KEY
     */
    @TableField("decrypt_key")
    private String decryptKey;

    /**
     * 通知状态
     */
    @TableField("notify_status")
    private String notifyStatus;

    /**
     * 通知路径
     */
    @TableField("notify_path")
    private String notifyPath;

    /**
     * kyc信息
     */
    @TableField("kyc_info")
    private String kycInfo;

    /**
     * 商户来源
     */
    @TableField("member_channel")
    private String memberChannel;

    /**
     * 企业注册地区
     */
    @TableField("register_region")
    private String registerRegion;

    /**
     * 卡核心商户编号
     */
    @TableField("reference_member_id")
    private String referenceMemberId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }
    public Long getSuperMemberId() {
        return superMemberId;
    }

    public void setSuperMemberId(Long superMemberId) {
        this.superMemberId = superMemberId;
    }
    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getSettledType() {
        return settledType;
    }

    public void setSettledType(String settledType) {
        this.settledType = settledType;
    }
    public String getOpenAccountType() {
        return openAccountType;
    }

    public void setOpenAccountType(String openAccountType) {
        this.openAccountType = openAccountType;
    }
    public String getAuditSeqNo() {
        return auditSeqNo;
    }

    public void setAuditSeqNo(String auditSeqNo) {
        this.auditSeqNo = auditSeqNo;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }
    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getCardFace() {
        return cardFace;
    }

    public void setCardFace(String cardFace) {
        this.cardFace = cardFace;
    }
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public String getApiStatus() {
        return apiStatus;
    }

    public void setApiStatus(String apiStatus) {
        this.apiStatus = apiStatus;
    }
    public String getDecryptKey() {
        return decryptKey;
    }

    public void setDecryptKey(String decryptKey) {
        this.decryptKey = decryptKey;
    }
    public String getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus;
    }
    public String getNotifyPath() {
        return notifyPath;
    }

    public void setNotifyPath(String notifyPath) {
        this.notifyPath = notifyPath;
    }
    public String getKycInfo() {
        return kycInfo;
    }

    public void setKycInfo(String kycInfo) {
        this.kycInfo = kycInfo;
    }
    public String getMemberChannel() {
        return memberChannel;
    }

    public void setMemberChannel(String memberChannel) {
        this.memberChannel = memberChannel;
    }
    public String getRegisterRegion() {
        return registerRegion;
    }

    public void setRegisterRegion(String registerRegion) {
        this.registerRegion = registerRegion;
    }
    public String getReferenceMemberId() {
        return referenceMemberId;
    }

    public void setReferenceMemberId(String referenceMemberId) {
        this.referenceMemberId = referenceMemberId;
    }

    @Override
    public String toString() {
        return "KCKycMainInfo{" +
            "id=" + id +
            ", memberId=" + memberId +
            ", superMemberId=" + superMemberId +
            ", step=" + step +
            ", email=" + email +
            ", settledType=" + settledType +
            ", openAccountType=" + openAccountType +
            ", auditSeqNo=" + auditSeqNo +
            ", remark=" + remark +
            ", auditStatus=" + auditStatus +
            ", englishName=" + englishName +
            ", status=" + status +
            ", cardFace=" + cardFace +
            ", createUser=" + createUser +
            ", createTime=" + createTime +
            ", updateUser=" + updateUser +
            ", updateTime=" + updateTime +
            ", apiStatus=" + apiStatus +
            ", decryptKey=" + decryptKey +
            ", notifyStatus=" + notifyStatus +
            ", notifyPath=" + notifyPath +
            ", kycInfo=" + kycInfo +
            ", memberChannel=" + memberChannel +
            ", registerRegion=" + registerRegion +
            ", referenceMemberId=" + referenceMemberId +
        "}";
    }
}
