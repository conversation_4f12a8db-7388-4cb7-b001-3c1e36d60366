package com.kun.linkage.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kun.linkage.common.db.entity.KycCertInfo;
import com.kun.linkage.common.db.vo.KLKycPushVO;
import lombok.Data;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface KycCertInfoMapper extends BaseMapper<KycCertInfo> {

    @Select("SELECT u.customer_id AS customerNo, u.customer_id AS kunCustomerNo, u.acs_customer_id AS acsCustomerNo, kyc.kyc_info AS kycInfo FROM vcc_kyc_cert_info kyc " +
            "LEFT JOIN vcc_user u ON kyc.user_id = u.id WHERE kyc.business_attribution = 2 AND kyc.status = 100 AND kyc.update_date between #{startDateTime} AND #{endDateTime}")
    List<KLKycPushVO> selectKLKycList(@Param("startDateTime") Date startDateTime, @Param("endDateTime") Date endDateTime);

}
