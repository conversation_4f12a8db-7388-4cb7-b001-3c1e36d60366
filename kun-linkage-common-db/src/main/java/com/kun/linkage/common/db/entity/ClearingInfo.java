package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 通用清分表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@TableName("kc_clearing_info")
public class ClearingInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 清算主键id
     */
    @TableId(value = "clearing_id", type = IdType.ASSIGN_ID)
    private Long clearingId;

    /**
     * 清算流水号
     */
    @TableField("clearing_no")
    private String clearingNo;

    /**
     * 关联清算文件主键id
     */
    @TableField("clearing_file_id")
    private Long clearingFileId;

    /**
     * 关联清算文件名称
     */
    @TableField("clearing_file_name")
    private String clearingFileName;

    /**
     * 通道来源;PBC-GW；
     */
    @TableField("channel_source")
    private String channelSource;

    /**
     * 来源系统:VCC;KL
     */
    @TableField("`system`")
    private String system;

    /**
     * 清算日期；yyyymmdd
     */
    @TableField("clearing_date")
    private LocalDate clearingDate;

    /**
     * 关联授权流水表的主键id
     */
    @TableField("auth_id")
    private Long authId;

    /**
     * 授权流水表中的交易id
     */
    @TableField("trans_id")
    private String transId;

    /**
     * 授权流水中的剩余可用金额；不更新
     */
    @TableField("auth_remain_auth_amt")
    private BigDecimal authRemainAuthAmt;

    /**
     * base ii文件里的trans_code;05:消费;06:退货；07：取现;25:消费查询;26:退货撤销；27：取现撤销
     */
    @TableField("trans_code")
    private String transCode;

    /**
     * 授权日期
     */
    @TableField("auth_date")
    private String authDate;

    /**
     * 原清算流水号
     */
    @TableField("original_clearing_no")
    private String originalClearingNo;

    /**
     * 商户id
     */
    @TableField("customer_mer_id")
    private String customerMerId;

    @TableField("card_acceptor_id")
    private String cardAcceptorId;

    /**
     * 商户名称
     */
    @TableField("card_acceptor_name")
    private String cardAcceptorName;

    /**
     * 商户国家代码
     */
    @TableField("card_acceptor_country_code")
    private String cardAcceptorCountryCode;

    /**
     * 终端号
     */
    @TableField("card_acceptor_tid")
    private String cardAcceptorTid;

    /**
     * 交易类型
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 交易时间;YYYYMMDD
     */
    @TableField("transaction_date")
    private String transactionDate;

    /**
     * 交易时间;YYYYMMDD hhmmss
     */
    @TableField("transaction_datetime")
    private String transactionDatetime;

    /**
     * 交易币种;三位数字
     */
    @TableField("transaction_currency_no")
    private String transactionCurrencyNo;

    /**
     * 交易币种;三位字母
     */
    @TableField("transaction_currency_code")
    private String transactionCurrencyCode;

    /**
     * 交易币种精度
     */
    @TableField("transaction_currency_precision")
    private Integer transactionCurrencyPrecision;

    /**
     * 清分金额
     */
    @TableField("clear_amount")
    private BigDecimal clearAmount;

    /**
     * 参考号;F37
     */
    @TableField("reference_no")
    private String referenceNo;

    /**
     * 审计追踪:F11
     */
    @TableField("trace_audit_no")
    private String traceAuditNo;

    /**
     * 卡id
     */
    @TableField("processor_card_id")
    private String processorCardId;

    /**
     * kcard系统卡id
     */
    @TableField("kcard_id")
    private String kcardId;

    /**
     * 加密卡号
     */
    @TableField("card_no")
    private String cardNo;

    /**
     * 脱敏卡号
     */
    @TableField("masked_card_no")
    private String maskedCardNo;

    /**
     * 持卡人币种;三位数字
     */
    @TableField("cardholder_currency_no")
    private String cardholderCurrencyNo;

    /**
     * 持卡人币种;三位字母
     */
    @TableField("cardholder_currency_code")
    private String cardholderCurrencyCode;

    /**
     * 持卡人币种精度
     */
    @TableField("cardholder_currency_precision")
    private Integer cardholderCurrencyPrecision;

    /**
     * 持卡人金额
     */
    @TableField("cardholder_amount")
    private BigDecimal cardholderAmount;

    /**
     * 收单参考号
     */
    @TableField("acq_arn")
    private String acqArn;

    /**
     * 交易金额
     */
    @TableField("auth_amount")
    private BigDecimal authAmount;

    /**
     * 差异标记
     */
    @TableField("difference_flag")
    private Integer differenceFlag;

    /**
     * 不含markup交易的差异金额
     */
    @TableField("transaction_amount_offset")
    private BigDecimal transactionAmountOffset;

    /**
     * 清算当时markup利率
     */
    @TableField("markup_rate")
    private BigDecimal markupRate;

    /**
     * 清算当时markup金额
     */
    @TableField("cardholder_markup_amount")
    private BigDecimal cardholderMarkupAmount;

    /**
     * 持卡人账单币种金额(含markup)
     */
    @TableField("cardholder_billing_amount_with_markup")
    private BigDecimal cardholderBillingAmountWithMarkup;

    /**
     * 清算状态;成功;失败(只有异常才会有失败)
     */
    @TableField("clearing_status")
    private String clearingStatus;

    /**
     * 是否异常;0:否；1:是;没有匹配到授权流水;以及arn没有匹配上原清分数据
     */
    @TableField("error_flag")
    private Integer errorFlag;
    /**
     * 差异原因
     */
    @TableField("error_reason")
    private String errorReason;
    /**
     * F38:清分文件授权码；
     */
    @TableField("auth_code")
    private String authCode;
    /**
     * PosEntryMode
     */
    @TableField("pos_entry_mode_tcr0")
    private String posEntryModeTcr0;

    /**
     * AcquiringIdentifier 
     */
    @TableField("acquiring_Identifier_tcr0")
    private String acquiringIdentifierTcr0;

    /**
     * MerchantCategoryCode
     */
    @TableField("card_acceptor_mcc")
    private String cardAcceptorMcc;

    /**
     * CentralProcessingDate格式:YYYYMMDD
     */
    @TableField("cpd")
    private String cpd;

    /**
     * InterchangeFeeAmount
     */
    @TableField("intechange_fee_amt")
    private BigDecimal intechangeFeeAmt;

    /**
     * InterchangeFeeSign
     */
    @TableField("intechange_fee_sign")
    private String intechangeFeeSign;

    /**
     * PosEnvironment
     */
    @TableField("pos_environment_tcr1")
    private String posEnvironmentTcr1;

    /**
     * Source Currency to Base 
Currency Exchange Rate 
     */
    @TableField("fx_rate_source_tcr5")
    private String fxRateSourceTcr5;

    /**
     * Base Currency to Destination 
Currency Exchange Rate 
     */
    @TableField("fx_rate_destination_tcr5")
    private String fxRateDestinationTcr5;

    /**
     * AuthorizationResponseCode
     */
    @TableField("authorization_response_code_tcr5")
    private String authorizationResponseCodeTcr5;

    /**
     * Multiple Clearing Sequence Number
     */
    @TableField("multiple_clearing_sequence_number_tcr5")
    private String multipleClearingSequenceNumberTcr5;

    /**
     * Multiple Clearing Sequence Count
     */
    @TableField("multiple_clearing_sequence_count_tcr5")
    private String multipleClearingSequenceCountTcr5;

    /**
     * Merchant Verification Value 
     */
    @TableField("mvv")
    private String mvv;

    /**
     * 撤销标记;0:未取消;1:已取消;
     */
    @TableField("reversal_flag")
    private Integer reversalFlag;

    /**
     * 通道请求流水号
     */
    @TableField("processor_request_id")
    private String processorRequestId;

    /**
     * 原交易通道请求流水号
     */
    @TableField("original_processor_request_id")
    private String originalProcessorRequestId;

    /**
     * FE交易号
     */
    @TableField("fe_transaction_number")
    private String feTransactionNumber;

    /**
     * 持卡人账单金额差额
     */
    @TableField("cardholder_billing_amount_offset")
    private BigDecimal cardholderBillingAmountOffset;

    /**
     * 持卡人账单金额差额(含markup)
     */
    @TableField("cardholder_markup_billing_amount_offset")
    private BigDecimal cardholderMarkupBillingAmountOffset;

    /**
     * 卡组产品ID
     */
    @TableField("card_schema_product_id")
    private String cardSchemaProductId;

    /**
     * 响应消息
     */
    @TableField("response_message")
    private String responseMessage;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableField("acquiring_id")
    private String acquiringId;
    /**
     * 清分类型
     */
    @TableField("clearing_type")
    private String clearingType;
    /**
     * 剩余结算金额
     */
    @TableField("remaining_clear_amount")
    private BigDecimal remainingClearAmount;
    /**
     * 原清算流水id
     */
    @TableField("original_clearing_id")
    private Long originalClearingId;

    /**
     * 授权流水剩余账单金额 不含markup
     */
    @TableField("auth_remain_bill_amt")
    private BigDecimal authRemainBillAmt;
    /**
     *授权流水剩余账单总金额（含markup）
     */
    @TableField("auth_remain_bill_amt_with_markup")
    private BigDecimal authRemainBillAmtWithMarkup;
    /**
     * 授权流水剩余冻结金额(含markUp)
     */
    @TableField("auth_remain_frozen_amt")
    private BigDecimal authRemainFrozenAmt;
    /**
     * 授权流水表中的markup利率
     */
    @TableField("auth_markup_rate")
    private BigDecimal authMarkupRate;
    /**
     * 是否通知;0,未通知;1:已经通知
     */
    @TableField("notify_flag")
    private Integer notifyFlag;
    /**
     * 通知结果;1:成功;2失败
     */
    @TableField("notify_results")
    private Integer notifyResults;
    /**
     * 卡产品编号
     */
    @TableField("card_product_code")
    private String cardProductCode;
    /**
     * Usage Code
     */
    @TableField("usage_cod")
    private String usageCod;
    /**
     * Reason Code
     */
    @TableField("reason_code")
    private String reasonCode;
    /**
     * Merchant Postal Code
     */
    @TableField("merchant_postal_code")
    private String merchantPostalCode;
    /**
     * merchant city
     */
    @TableField("merchant_city")
    private String merchantCity;

    /**
     * 持卡人利率
     */
    @TableField("cardholder_billing_rate")
    private BigDecimal cardholderBillingRate;

    /**
     * 持卡人利率来源
     */
    @TableField("cardholder_billing_rate_source")
    private String cardholderBillingRateSource;


    /**
     * 清分文件中的目标币种,3位字母
     */
    @TableField("destination_currency_code")
    private String destinationCurrencyCode;


    /**
     * 清分文件中的目标金额
     */
    @TableField("destination_currency_amount")
    private BigDecimal destinationCurrencyAmount;

    /**
     * settlementFlag
     */
    @TableField("settlement_flag")
    private String settlementFlag;

    /**
     * 删除标记;0:未删除;1:已删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;


    public Long getClearingId() {
        return clearingId;
    }

    public void setClearingId(Long clearingId) {
        this.clearingId = clearingId;
    }

    public String getClearingNo() {
        return clearingNo;
    }

    public void setClearingNo(String clearingNo) {
        this.clearingNo = clearingNo;
    }

    public Long getClearingFileId() {
        return clearingFileId;
    }

    public void setClearingFileId(Long clearingFileId) {
        this.clearingFileId = clearingFileId;
    }

    public String getClearingFileName() {
        return clearingFileName;
    }

    public void setClearingFileName(String clearingFileName) {
        this.clearingFileName = clearingFileName;
    }

    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public LocalDate getClearingDate() {
        return clearingDate;
    }

    public void setClearingDate(LocalDate clearingDate) {
        this.clearingDate = clearingDate;
    }

    public Long getAuthId() {
        return authId;
    }

    public void setAuthId(Long authId) {
        this.authId = authId;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public BigDecimal getAuthRemainAuthAmt() {
        return authRemainAuthAmt;
    }

    public void setAuthRemainAuthAmt(BigDecimal authRemainAuthAmt) {
        this.authRemainAuthAmt = authRemainAuthAmt;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getAuthDate() {
        return authDate;
    }

    public void setAuthDate(String authDate) {
        this.authDate = authDate;
    }

    public String getOriginalClearingNo() {
        return originalClearingNo;
    }

    public void setOriginalClearingNo(String originalClearingNo) {
        this.originalClearingNo = originalClearingNo;
    }

    public String getCustomerMerId() {
        return customerMerId;
    }

    public void setCustomerMerId(String customerMerId) {
        this.customerMerId = customerMerId;
    }

    public String getCardAcceptorId() {
        return cardAcceptorId;
    }

    public void setCardAcceptorId(String cardAcceptorId) {
        this.cardAcceptorId = cardAcceptorId;
    }

    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }

    public String getCardAcceptorCountryCode() {
        return cardAcceptorCountryCode;
    }

    public void setCardAcceptorCountryCode(String cardAcceptorCountryCode) {
        this.cardAcceptorCountryCode = cardAcceptorCountryCode;
    }

    public String getCardAcceptorTid() {
        return cardAcceptorTid;
    }

    public void setCardAcceptorTid(String cardAcceptorTid) {
        this.cardAcceptorTid = cardAcceptorTid;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionDatetime() {
        return transactionDatetime;
    }

    public void setTransactionDatetime(String transactionDatetime) {
        this.transactionDatetime = transactionDatetime;
    }

    public String getTransactionCurrencyNo() {
        return transactionCurrencyNo;
    }

    public void setTransactionCurrencyNo(String transactionCurrencyNo) {
        this.transactionCurrencyNo = transactionCurrencyNo;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public Integer getTransactionCurrencyPrecision() {
        return transactionCurrencyPrecision;
    }

    public void setTransactionCurrencyPrecision(Integer transactionCurrencyPrecision) {
        this.transactionCurrencyPrecision = transactionCurrencyPrecision;
    }

    public BigDecimal getClearAmount() {
        return clearAmount;
    }

    public void setClearAmount(BigDecimal clearAmount) {
        this.clearAmount = clearAmount;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getTraceAuditNo() {
        return traceAuditNo;
    }

    public void setTraceAuditNo(String traceAuditNo) {
        this.traceAuditNo = traceAuditNo;
    }

    public String getProcessorCardId() {
        return processorCardId;
    }

    public void setProcessorCardId(String processorCardId) {
        this.processorCardId = processorCardId;
    }

    public String getKcardId() {
        return kcardId;
    }

    public void setKcardId(String kcardId) {
        this.kcardId = kcardId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }


    public String getCardholderCurrencyNo() {
        return cardholderCurrencyNo;
    }

    public void setCardholderCurrencyNo(String cardholderCurrencyNo) {
        this.cardholderCurrencyNo = cardholderCurrencyNo;
    }

    public String getCardholderCurrencyCode() {
        return cardholderCurrencyCode;
    }

    public void setCardholderCurrencyCode(String cardholderCurrencyCode) {
        this.cardholderCurrencyCode = cardholderCurrencyCode;
    }

    public Integer getCardholderCurrencyPrecision() {
        return cardholderCurrencyPrecision;
    }

    public void setCardholderCurrencyPrecision(Integer cardholderCurrencyPrecision) {
        this.cardholderCurrencyPrecision = cardholderCurrencyPrecision;
    }

    public BigDecimal getCardholderAmount() {
        return cardholderAmount;
    }

    public void setCardholderAmount(BigDecimal cardholderAmount) {
        this.cardholderAmount = cardholderAmount;
    }

    public String getAcqArn() {
        return acqArn;
    }

    public void setAcqArn(String acqArn) {
        this.acqArn = acqArn;
    }

    public BigDecimal getAuthAmount() {
        return authAmount;
    }

    public void setAuthAmount(BigDecimal authAmount) {
        this.authAmount = authAmount;
    }

    public Integer getDifferenceFlag() {
        return differenceFlag;
    }

    public void setDifferenceFlag(Integer differenceFlag) {
        this.differenceFlag = differenceFlag;
    }

    public BigDecimal getTransactionAmountOffset() {
        return transactionAmountOffset;
    }

    public void setTransactionAmountOffset(BigDecimal transactionAmountOffset) {
        this.transactionAmountOffset = transactionAmountOffset;
    }

    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }

    public BigDecimal getCardholderMarkupAmount() {
        return cardholderMarkupAmount;
    }

    public void setCardholderMarkupAmount(BigDecimal cardholderMarkupAmount) {
        this.cardholderMarkupAmount = cardholderMarkupAmount;
    }

    public BigDecimal getCardholderBillingAmountWithMarkup() {
        return cardholderBillingAmountWithMarkup;
    }

    public void setCardholderBillingAmountWithMarkup(BigDecimal cardholderBillingAmountWithMarkup) {
        this.cardholderBillingAmountWithMarkup = cardholderBillingAmountWithMarkup;
    }

    public String getClearingStatus() {
        return clearingStatus;
    }

    public void setClearingStatus(String clearingStatus) {
        this.clearingStatus = clearingStatus;
    }

    public Integer getErrorFlag() {
        return errorFlag;
    }

    public void setErrorFlag(Integer errorFlag) {
        this.errorFlag = errorFlag;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getPosEntryModeTcr0() {
        return posEntryModeTcr0;
    }

    public void setPosEntryModeTcr0(String posEntryModeTcr0) {
        this.posEntryModeTcr0 = posEntryModeTcr0;
    }

    public String getAcquiringIdentifierTcr0() {
        return acquiringIdentifierTcr0;
    }

    public void setAcquiringIdentifierTcr0(String acquiringIdentifierTcr0) {
        this.acquiringIdentifierTcr0 = acquiringIdentifierTcr0;
    }

    public String getCardAcceptorMcc() {
        return cardAcceptorMcc;
    }

    public void setCardAcceptorMcc(String cardAcceptorMcc) {
        this.cardAcceptorMcc = cardAcceptorMcc;
    }

    public String getCpd() {
        return cpd;
    }

    public void setCpd(String cpd) {
        this.cpd = cpd;
    }

    public BigDecimal getIntechangeFeeAmt() {
        return intechangeFeeAmt;
    }

    public void setIntechangeFeeAmt(BigDecimal intechangeFeeAmt) {
        this.intechangeFeeAmt = intechangeFeeAmt;
    }

    public String getIntechangeFeeSign() {
        return intechangeFeeSign;
    }

    public void setIntechangeFeeSign(String intechangeFeeSign) {
        this.intechangeFeeSign = intechangeFeeSign;
    }

    public String getPosEnvironmentTcr1() {
        return posEnvironmentTcr1;
    }

    public void setPosEnvironmentTcr1(String posEnvironmentTcr1) {
        this.posEnvironmentTcr1 = posEnvironmentTcr1;
    }

    public String getFxRateSourceTcr5() {
        return fxRateSourceTcr5;
    }

    public void setFxRateSourceTcr5(String fxRateSourceTcr5) {
        this.fxRateSourceTcr5 = fxRateSourceTcr5;
    }

    public String getFxRateDestinationTcr5() {
        return fxRateDestinationTcr5;
    }

    public void setFxRateDestinationTcr5(String fxRateDestinationTcr5) {
        this.fxRateDestinationTcr5 = fxRateDestinationTcr5;
    }

    public String getAuthorizationResponseCodeTcr5() {
        return authorizationResponseCodeTcr5;
    }

    public void setAuthorizationResponseCodeTcr5(String authorizationResponseCodeTcr5) {
        this.authorizationResponseCodeTcr5 = authorizationResponseCodeTcr5;
    }

    public String getMultipleClearingSequenceNumberTcr5() {
        return multipleClearingSequenceNumberTcr5;
    }

    public void setMultipleClearingSequenceNumberTcr5(String multipleClearingSequenceNumberTcr5) {
        this.multipleClearingSequenceNumberTcr5 = multipleClearingSequenceNumberTcr5;
    }

    public String getMultipleClearingSequenceCountTcr5() {
        return multipleClearingSequenceCountTcr5;
    }

    public void setMultipleClearingSequenceCountTcr5(String multipleClearingSequenceCountTcr5) {
        this.multipleClearingSequenceCountTcr5 = multipleClearingSequenceCountTcr5;
    }

    public String getMvv() {
        return mvv;
    }

    public void setMvv(String mvv) {
        this.mvv = mvv;
    }

    public Integer getReversalFlag() {
        return reversalFlag;
    }

    public void setReversalFlag(Integer reversalFlag) {
        this.reversalFlag = reversalFlag;
    }

    public String getProcessorRequestId() {
        return processorRequestId;
    }

    public void setProcessorRequestId(String processorRequestId) {
        this.processorRequestId = processorRequestId;
    }

    public String getOriginalProcessorRequestId() {
        return originalProcessorRequestId;
    }

    public void setOriginalProcessorRequestId(String originalProcessorRequestId) {
        this.originalProcessorRequestId = originalProcessorRequestId;
    }

    public String getFeTransactionNumber() {
        return feTransactionNumber;
    }

    public void setFeTransactionNumber(String feTransactionNumber) {
        this.feTransactionNumber = feTransactionNumber;
    }

    public BigDecimal getCardholderBillingAmountOffset() {
        return cardholderBillingAmountOffset;
    }

    public void setCardholderBillingAmountOffset(BigDecimal cardholderBillingAmountOffset) {
        this.cardholderBillingAmountOffset = cardholderBillingAmountOffset;
    }

    public BigDecimal getCardholderMarkupBillingAmountOffset() {
        return cardholderMarkupBillingAmountOffset;
    }

    public void setCardholderMarkupBillingAmountOffset(BigDecimal cardholderMarkupBillingAmountOffset) {
        this.cardholderMarkupBillingAmountOffset = cardholderMarkupBillingAmountOffset;
    }

    public String getCardSchemaProductId() {
        return cardSchemaProductId;
    }

    public void setCardSchemaProductId(String cardSchemaProductId) {
        this.cardSchemaProductId = cardSchemaProductId;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAcquiringId() {
        return acquiringId;
    }

    public void setAcquiringId(String acquiringId) {
        this.acquiringId = acquiringId;
    }

    public String getClearingType() {
        return clearingType;
    }

    public void setClearingType(String clearingType) {
        this.clearingType = clearingType;
    }

    public BigDecimal getRemainingClearAmount() {
        return remainingClearAmount;
    }

    public void setRemainingClearAmount(BigDecimal remainingClearAmount) {
        this.remainingClearAmount = remainingClearAmount;
    }

    public Long getOriginalClearingId() {
        return originalClearingId;
    }

    public void setOriginalClearingId(Long originalClearingId) {
        this.originalClearingId = originalClearingId;
    }

    public BigDecimal getAuthRemainBillAmt() {
        return authRemainBillAmt;
    }

    public void setAuthRemainBillAmt(BigDecimal authRemainBillAmt) {
        this.authRemainBillAmt = authRemainBillAmt;
    }

    public BigDecimal getAuthRemainBillAmtWithMarkup() {
        return authRemainBillAmtWithMarkup;
    }

    public void setAuthRemainBillAmtWithMarkup(BigDecimal authRemainBillAmtWithMarkup) {
        this.authRemainBillAmtWithMarkup = authRemainBillAmtWithMarkup;
    }

    public BigDecimal getAuthRemainFrozenAmt() {
        return authRemainFrozenAmt;
    }

    public void setAuthRemainFrozenAmt(BigDecimal authRemainFrozenAmt) {
        this.authRemainFrozenAmt = authRemainFrozenAmt;
    }

    public BigDecimal getAuthMarkupRate() {
        return authMarkupRate;
    }

    public void setAuthMarkupRate(BigDecimal authMarkupRate) {
        this.authMarkupRate = authMarkupRate;
    }

    public Integer getNotifyFlag() {
        return notifyFlag;
    }

    public void setNotifyFlag(Integer notifyFlag) {
        this.notifyFlag = notifyFlag;
    }

    public Integer getNotifyResults() {
        return notifyResults;
    }

    public void setNotifyResults(Integer notifyResults) {
        this.notifyResults = notifyResults;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getUsageCod() {
        return usageCod;
    }

    public void setUsageCod(String usageCod) {
        this.usageCod = usageCod;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getMerchantPostalCode() {
        return merchantPostalCode;
    }

    public void setMerchantPostalCode(String merchantPostalCode) {
        this.merchantPostalCode = merchantPostalCode;
    }

    public String getMerchantCity() {
        return merchantCity;
    }

    public void setMerchantCity(String merchantCity) {
        this.merchantCity = merchantCity;
    }

    public BigDecimal getCardholderBillingRate() {
        return cardholderBillingRate;
    }

    public void setCardholderBillingRate(BigDecimal cardholderBillingRate) {
        this.cardholderBillingRate = cardholderBillingRate;
    }

    public String getCardholderBillingRateSource() {
        return cardholderBillingRateSource;
    }

    public void setCardholderBillingRateSource(String cardholderBillingRateSource) {
        this.cardholderBillingRateSource = cardholderBillingRateSource;
    }

    public String getDestinationCurrencyCode() {
        return destinationCurrencyCode;
    }

    public void setDestinationCurrencyCode(String destinationCurrencyCode) {
        this.destinationCurrencyCode = destinationCurrencyCode;
    }

    public BigDecimal getDestinationCurrencyAmount() {
        return destinationCurrencyAmount;
    }

    public void setDestinationCurrencyAmount(BigDecimal destinationCurrencyAmount) {
        this.destinationCurrencyAmount = destinationCurrencyAmount;
    }

    public String getSettlementFlag() {
        return settlementFlag;
    }

    public void setSettlementFlag(String settlementFlag) {
        this.settlementFlag = settlementFlag;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String toString() {
        return "ClearingInfo{" +
            "clearingId=" + clearingId +
            ", clearingNo=" + clearingNo +
            ", clearingFileId=" + clearingFileId +
            ", clearingFileName=" + clearingFileName +
            ", channelSource=" + channelSource +
            ", system=" + system +
            ", clearingDate=" + clearingDate +
            ", authId=" + authId +
            ", transId=" + transId +
            ", authRemainAuthAmt=" + authRemainAuthAmt +
            ", transCode=" + transCode +
            ", authDate=" + authDate +
            ", originalClearingNo=" + originalClearingNo +
            ", customerMerId=" + customerMerId +
            ", cardAcceptorId=" + cardAcceptorId +
            ", cardAcceptorName=" + cardAcceptorName +
            ", cardAcceptorCountryCode=" + cardAcceptorCountryCode +
            ", cardAcceptorTid=" + cardAcceptorTid +
            ", transactionType=" + transactionType +
            ", transactionDate=" + transactionDate +
            ", transactionDatetime=" + transactionDatetime +
            ", transactionCurrencyNo=" + transactionCurrencyNo +
            ", transactionCurrencyCode=" + transactionCurrencyCode +
            ", transactionCurrencyPrecision=" + transactionCurrencyPrecision +
            ", clearAmount=" + clearAmount +
            ", referenceNo=" + referenceNo +
            ", traceAuditNo=" + traceAuditNo +
            ", processorCardId=" + processorCardId +
            ", kcardId=" + kcardId +
            ", cardNo=" + cardNo +
            ", maskedCardNo=" + maskedCardNo +
            ", cardholderCurrencyNo=" + cardholderCurrencyNo +
            ", cardholderCurrencyCode=" + cardholderCurrencyCode +
            ", cardholderCurrencyPrecision=" + cardholderCurrencyPrecision +
            ", cardholderAmount=" + cardholderAmount +
            ", acqArn=" + acqArn +
            ", authAmount=" + authAmount +
            ", differenceFlag=" + differenceFlag +
            ", transactionAmountOffset=" + transactionAmountOffset +
            ", markupRate=" + markupRate +
            ", cardholderMarkupAmount=" + cardholderMarkupAmount +
            ", cardholderBillingAmountWithMarkup=" + cardholderBillingAmountWithMarkup +
            ", clearingStatus=" + clearingStatus +
            ", errorFlag=" + errorFlag +
            ", errorReason=" + errorReason +
            ", authCode=" + authCode +
            ", posEntryModeTcr0=" + posEntryModeTcr0 +
            ", acquiringIdentifierTcr0=" + acquiringIdentifierTcr0 +
            ", cardAcceptorMcc=" + cardAcceptorMcc +
            ", cpd=" + cpd +
            ", intechangeFeeAmt=" + intechangeFeeAmt +
            ", intechangeFeeSign=" + intechangeFeeSign +
            ", posEnvironmentTcr1=" + posEnvironmentTcr1 +
            ", fxRateSourceTcr5=" + fxRateSourceTcr5 +
            ", fxRateDestinationTcr5=" + fxRateDestinationTcr5 +
            ", authorizationResponseCodeTcr5=" + authorizationResponseCodeTcr5 +
            ", multipleClearingSequenceNumberTcr5=" + multipleClearingSequenceNumberTcr5 +
            ", multipleClearingSequenceCountTcr5=" + multipleClearingSequenceCountTcr5 +
            ", mvv=" + mvv +
            ", reversalFlag=" + reversalFlag +
            ", processorRequestId=" + processorRequestId +
            ", originalProcessorRequestId=" + originalProcessorRequestId +
            ", feTransactionNumber=" + feTransactionNumber +
            ", cardholderBillingAmountOffset=" + cardholderBillingAmountOffset +
            ", cardholderMarkupBillingAmountOffset=" + cardholderMarkupBillingAmountOffset +
            ", cardSchemaProductId=" + cardSchemaProductId +
            ", responseMessage=" + responseMessage +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", acquiringId=" + acquiringId +
            ", clearingType=" + clearingType +
            ", remainingClearAmount=" + remainingClearAmount +
            ", originalClearingId=" + originalClearingId +
            ", authRemainBillAmt=" + authRemainBillAmt +
            ", authRemainBillAmtWithMarkup=" + authRemainBillAmtWithMarkup +
            ", authRemainFrozenAmt=" + authRemainFrozenAmt +
            ", authMarkupRate=" + authMarkupRate +
            ", notifyFlag=" + notifyFlag +
            ", notifyResults=" + notifyResults +
            ", cardProductCode=" + cardProductCode +
            ", usageCod=" + usageCod +
            ", reasonCode=" + reasonCode +
            ", merchantPostalCode=" + merchantPostalCode +
            ", merchantCity=" + merchantCity +
            ", cardholderBillingRate=" + cardholderBillingRate +
            ", cardholderBillingRateSource=" + cardholderBillingRateSource +
            ", destinationCurrencyCode=" + destinationCurrencyCode +
            ", destinationCurrencyAmount=" + destinationCurrencyAmount +
            ", settlementFlag=" + settlementFlag +
            ", deleteFlag=" + deleteFlag +
        "}";
    }
}
