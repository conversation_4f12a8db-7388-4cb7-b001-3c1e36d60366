package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 卡核心回调通知记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@TableName("kc_auth_flow")
public class KCAuthFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键 id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 渠道类型 
     */
    @TableField("channel")
    private String channel;

    /**
     * 请求流水号
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 卡ID
     */
    @TableField("card_id")
    private String cardId;

    /**
     * 交易类型
     */
    @TableField("trans_type")
    private Integer transType;

    /**
     * 授权类型
     */
    @TableField("authorization_type")
    private Integer authorizationType;

    /**
     * 财务方向
     */
    @TableField("accounting_direction")
    private Integer accountingDirection;

    /**
     * 参考号
     */
    @TableField("reference_no")
    private String referenceNo;

    /**
     * 交易ID
     */
    @TableField("trans_id")
    private String transId;

    /**
     * 原交易ID
     */
    @TableField("original_trans_id")
    private String originalTransId;

    /**
     * 原交易完成时间 yyyy-MM-dd HH:mm:ss
     */
    @TableField("original_finish_time")
    private Date originalFinishTime;

    /**
     * 原交易完成日期 yyyy-MM-dd
     */
    @TableField("original_finish_date")
    private Date originalFinishDate;

    /**
     * 处理码
     */
    @TableField("processing_code")
    private String processingCode;

    /**
     * 主账号
     */
    @TableField("primary_account_number")
    private String primaryAccountNumber;

    /**
     * 账户金额
     */
    @TableField("amount_account")
    private String amountAccount;

    /**
     * 检索参考号
     */
    @TableField("retrieval_reference_number")
    private String retrievalReferenceNumber;

    /**
     * 持卡人账单货币代码
     */
    @TableField("currency_code_cardholder_billing")
    private String currencyCodeCardholderBilling;

    /**
     * 交易货币代码
     */
    @TableField("currency_code_transaction")
    private String currencyCodeTransaction;

    /**
     * 持卡人账单汇率
     */
    @TableField("conversion_rate_cardholder_billing")
    private String conversionRateCardholderBilling;

    /**
     * 系统跟踪审计号
     */
    @TableField("systems_trace_audit_number")
    private String systemsTraceAuditNumber;

    /**
     * 账户货币代码
     */
    @TableField("currency_code_account")
    private String currencyCodeAccount;

    /**
     * 报文类型
     */
    @TableField("message_type")
    private String messageType;

    /**
     * 账户识别
     */
    @TableField("account_identification")
    private String accountIdentification;

    /**
     * 终端输出能力
     */
    @TableField("terminal_output_capability")
    private String terminalOutputCapability;

    /**
     * PIN采集能力
     */
    @TableField("pin_capture_capability")
    private String pinCaptureCapability;

    /**
     * 卡数据输出能力
     */
    @TableField("card_data_output_capability")
    private String cardDataOutputCapability;

    /**
     * 持卡人认证能力
     */
    @TableField("cardholder_authentication_capability")
    private String cardholderAuthenticationCapability;

    /**
     * 持卡人认证方式
     */
    @TableField("cardholder_authentication_method")
    private String cardholderAuthenticationMethod;

    /**
     * 卡片存在标志
     */
    @TableField("card_presence")
    private String cardPresence;

    /**
     * 操作环境
     */
    @TableField("operating_environmen")
    private String operatingEnvironmen;

    /**
     * 卡片采集能力
     */
    @TableField("card_capture_capabilit")
    private String cardCaptureCapabilit;

    /**
     * 卡数据输入能力
     */
    @TableField("card_data_input_capability")
    private String cardDataInputCapability;

    /**
     * 卡数据输入方式
     */
    @TableField("card_data_input_mode")
    private String cardDataInputMode;

    /**
     * 持卡人存在标志
     */
    @TableField("cardholder_presence_indicator")
    private String cardholderPresenceIndicator;

    /**
     * 持卡人认证主体
     */
    @TableField("cardholder_authentication_entity")
    private String cardholderAuthenticationEntity;

    /**
     * 账户货币金额
     */
    @TableField("transaction_amount_account_currency")
    private String transactionAmountAccountCurrency;

    /**
     * 收单行费用
     */
    @TableField("acquirer_fee_amount")
    private String acquirerFeeAmount;

    /**
     * 费用符号
     */
    @TableField("fee_sign")
    private String feeSign;

    /**
     * 费用货币
     */
    @TableField("fee_currency")
    private String feeCurrency;

    /**
     * 费用金额
     */
    @TableField("fee_amoun")
    private String feeAmoun;

    /**
     * 清算类型
     */
    @TableField("settlement_type")
    private String settlementType;

    /**
     * 发送方参考号
     */
    @TableField("sender_reference_number")
    private String senderReferenceNumber;

    /**
     * 客户手机号
     */
    @TableField("customer_mobile_phone")
    private String customerMobilePhone;

    /**
     * SVFE交易类型
     */
    @TableField("svfe_transaction_type")
    private String svfeTransactionType;

    /**
     * 卡片类型
     */
    @TableField("card_type")
    private String cardType;

    /**
     * 本地交易时间
     */
    @TableField("local_transaction_date_and_time")
    private String localTransactionDateAndTime;

    /**
     * 商户类型（mcc码）
     */
    @TableField("merchant_type")
    private String merchantType;

    /**
     * 收单机构代码
     */
    @TableField("acquier_institue_identifier")
    private String acquierInstitueIdentifier;

    /**
     * 收单机构国家代码
     */
    @TableField("acquirer_country_code")
    private String acquirerCountryCode;

    /**
     * 收单机构网络标识
     */
    @TableField("acquirer_network_identifier")
    private String acquirerNetworkIdentifier;

    /**
     * BPC卡ID
     */
    @TableField("processor_card_id")
    private String processorCardId;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * FE跟踪号
     */
    @TableField("fe_trace_number")
    private String feTraceNumber;

    /**
     * FE交易时间
     */
    @TableField("fe_transaction_date_and_time")
    private String feTransactionDateAndTime;

    /**
     * FE交易号
     */
    @TableField("fe_transaction_number")
    private String feTransactionNumber;

    /**
     * 发卡机构网络标识
     */
    @TableField("issuer_network_identifier")
    private String issuerNetworkIdentifier;

    /**
     * 网络参考数据
     */
    @TableField("network_reference_data")
    private String networkReferenceData;

    /**
     * 交易风险评分
     */
    @TableField("transaction_risk_score")
    private String transactionRiskScore;

    /**
     * 卡受理机构标识
     */
    @TableField("card_acceptor_identification_code")
    private String cardAcceptorIdentificationCode;

    /**
     * 传输日期时间
     */
    @TableField("transmission_date_and_time")
    private String transmissionDateAndTime;

    /**
     * 账户汇率
     */
    @TableField("conversion_rate_account")
    private String conversionRateAccount;

    /**
     * 卡人账单金额
     */
    @TableField("amount_cardholder_billing")
    private String amountCardholderBilling;

    /**
     * 本地交易日期时间
     */
    @TableField("date_time_local_transaction")
    private String dateTimeLocalTransaction;

    /**
     * 交易金额
     */
    @TableField("amount_transaction")
    private String amountTransaction;

    /**
     * 清算日期
     */
    @TableField("settlement_date")
    private String settlementDate;

    /**
     * 银行商户名
     */
    @TableField("card_acceptor_name")
    private String cardAcceptorName;

    /**
     * 银行商户所在城市
     */
    @TableField("card_acceptor_city")
    private String cardAcceptorCity;

    /**
     * 国家代码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 收单机构代码
     */
    @TableField("acquiring_institution_identification_code")
    private String acquiringInstitutionIdentificationCode;

    /**
     * SVFE发卡机构标识
     */
    @TableField("svfe_issuer_institution_identifier")
    private String svfeIssuerInstitutionIdentifier;

    /**
     * 终端标识
     */
    @TableField("card_acceptor_terminal_identification")
    private String cardAcceptorTerminalIdentification;

    /**
     * 授权类型 普通授权 预授权
     */
    @TableField("auth_type")
    private String authType;

    /**
     * 授权开始时间
     */
    @TableField("auth_start_time")
    private Date authStartTime;

    /**
     * 授权到期时间
     */
    @TableField("auth_end_time")
    private Date authEndTime;

    /**
     * markup比例
     */
    @TableField("markup_rate")
    private BigDecimal markupRate;

    /**
     * 交易金额 不含markup金额，不会变动
     */
    @TableField("trans_amt")
    private BigDecimal transAmt;

    /**
     * 账单金额 不含markup金额，不会变动
     */
    @TableField("bill_amount")
    private BigDecimal billAmount;

    /**
     * 账单金额 含markup金额，不会变动
     */
    @TableField("bill_amount_with_markup")
    private BigDecimal billAmountWithMarkup;

    /**
     * 交易币种金额汇总  在授权、授权追加时增加，撤销、退款时减少，在撤销、退款时，判断交易币种金额是否超出，若超出，直接返回失败
     */
    @TableField("remain_auth_amt")
    private BigDecimal remainAuthAmt;

    /**
     * 账单金额汇总（不含markup），在授权、授权追加时增加，撤销、清算时减少，（清算时比对这个金额是否超额清算）。退款时不受影响
     */
    @TableField("remain_bill_amt")
    private BigDecimal remainBillAmt;

    /**
     * 剩余账单总金额（含markup）在授权、授权追加时增加，撤销、退款时减少，在撤销、退款时，若账单金额大于这个金额，则取表里这个金额进行撤销或者退款
     */
    @TableField("remain_bill_amt_with_markup")
    private BigDecimal remainBillAmtWithMarkup;

    /**
     * 剩余冻结金额 含markup的账单金额汇总，在授权、授权追加增加，撤销、清算时减少，这个字段用于在清算之后进行授权到期释放
     */
    @TableField("remain_frozen_amt")
    private BigDecimal remainFrozenAmt;

    /**
     * 所属系统 VCC KL
     */
    @TableField("system_mark")
    private String systemMark;

    /**
     * kcard商户编号
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * CRMID
     */
    @TableField("merchant_id")
    private String merchantId;

    /**
     * 状态 SUCC 成功  FAIL 失败
     */
    @TableField("status")
    private String status;

    /**
     * 清算状态
     */
    @TableField("clear_status")
    private Integer clearStatus;

    /**
     * 额度释放状态 WAIT  待释放，SUCC 已释放，FAIL 释放失败
     */
    @TableField("release_status")
    private String releaseStatus;

    /**
     * 额度释放时间
     */
    @TableField("release_time")
    private Date releaseTime;

    /**
     * 额度释放金额
     */
    @TableField("release_amt")
    private BigDecimal releaseAmt;

    /**
     * 授权决策：0 同意，1 拒绝
     */
    @TableField("authorization_decision")
    private Integer authorizationDecision;

    /**
     * 授权完成时间
     */
    @TableField("auth_completion_time")
    private Date authCompletionTime;

    /**
     * 返回码
     */
    @TableField("return_code")
    private String returnCode;

    /**
     * 返回信息
     */
    @TableField("return_message")
    private String returnMessage;

    /**
     * 授权码
     */
    @TableField("approve_code")
    private String approveCode;

    /**
     * 超时标识
     */
    @TableField("timeout_flag")
    private Integer timeoutFlag;

    /**
     * 创建日期
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 原数据
     */
    @TableField("original_data")
    private String originalData;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public Integer getTransType() {
        return transType;
    }

    public void setTransType(Integer transType) {
        this.transType = transType;
    }
    public Integer getAuthorizationType() {
        return authorizationType;
    }

    public void setAuthorizationType(Integer authorizationType) {
        this.authorizationType = authorizationType;
    }
    public Integer getAccountingDirection() {
        return accountingDirection;
    }

    public void setAccountingDirection(Integer accountingDirection) {
        this.accountingDirection = accountingDirection;
    }
    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }
    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }
    public String getOriginalTransId() {
        return originalTransId;
    }

    public void setOriginalTransId(String originalTransId) {
        this.originalTransId = originalTransId;
    }
    public Date getOriginalFinishTime() {
        return originalFinishTime;
    }

    public void setOriginalFinishTime(Date originalFinishTime) {
        this.originalFinishTime = originalFinishTime;
    }
    public Date getOriginalFinishDate() {
        return originalFinishDate;
    }

    public void setOriginalFinishDate(Date originalFinishDate) {
        this.originalFinishDate = originalFinishDate;
    }
    public String getProcessingCode() {
        return processingCode;
    }

    public void setProcessingCode(String processingCode) {
        this.processingCode = processingCode;
    }
    public String getPrimaryAccountNumber() {
        return primaryAccountNumber;
    }

    public void setPrimaryAccountNumber(String primaryAccountNumber) {
        this.primaryAccountNumber = primaryAccountNumber;
    }
    public String getAmountAccount() {
        return amountAccount;
    }

    public void setAmountAccount(String amountAccount) {
        this.amountAccount = amountAccount;
    }
    public String getRetrievalReferenceNumber() {
        return retrievalReferenceNumber;
    }

    public void setRetrievalReferenceNumber(String retrievalReferenceNumber) {
        this.retrievalReferenceNumber = retrievalReferenceNumber;
    }
    public String getCurrencyCodeCardholderBilling() {
        return currencyCodeCardholderBilling;
    }

    public void setCurrencyCodeCardholderBilling(String currencyCodeCardholderBilling) {
        this.currencyCodeCardholderBilling = currencyCodeCardholderBilling;
    }
    public String getCurrencyCodeTransaction() {
        return currencyCodeTransaction;
    }

    public void setCurrencyCodeTransaction(String currencyCodeTransaction) {
        this.currencyCodeTransaction = currencyCodeTransaction;
    }
    public String getConversionRateCardholderBilling() {
        return conversionRateCardholderBilling;
    }

    public void setConversionRateCardholderBilling(String conversionRateCardholderBilling) {
        this.conversionRateCardholderBilling = conversionRateCardholderBilling;
    }
    public String getSystemsTraceAuditNumber() {
        return systemsTraceAuditNumber;
    }

    public void setSystemsTraceAuditNumber(String systemsTraceAuditNumber) {
        this.systemsTraceAuditNumber = systemsTraceAuditNumber;
    }
    public String getCurrencyCodeAccount() {
        return currencyCodeAccount;
    }

    public void setCurrencyCodeAccount(String currencyCodeAccount) {
        this.currencyCodeAccount = currencyCodeAccount;
    }
    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }
    public String getAccountIdentification() {
        return accountIdentification;
    }

    public void setAccountIdentification(String accountIdentification) {
        this.accountIdentification = accountIdentification;
    }
    public String getTerminalOutputCapability() {
        return terminalOutputCapability;
    }

    public void setTerminalOutputCapability(String terminalOutputCapability) {
        this.terminalOutputCapability = terminalOutputCapability;
    }
    public String getPinCaptureCapability() {
        return pinCaptureCapability;
    }

    public void setPinCaptureCapability(String pinCaptureCapability) {
        this.pinCaptureCapability = pinCaptureCapability;
    }
    public String getCardDataOutputCapability() {
        return cardDataOutputCapability;
    }

    public void setCardDataOutputCapability(String cardDataOutputCapability) {
        this.cardDataOutputCapability = cardDataOutputCapability;
    }
    public String getCardholderAuthenticationCapability() {
        return cardholderAuthenticationCapability;
    }

    public void setCardholderAuthenticationCapability(String cardholderAuthenticationCapability) {
        this.cardholderAuthenticationCapability = cardholderAuthenticationCapability;
    }
    public String getCardholderAuthenticationMethod() {
        return cardholderAuthenticationMethod;
    }

    public void setCardholderAuthenticationMethod(String cardholderAuthenticationMethod) {
        this.cardholderAuthenticationMethod = cardholderAuthenticationMethod;
    }
    public String getCardPresence() {
        return cardPresence;
    }

    public void setCardPresence(String cardPresence) {
        this.cardPresence = cardPresence;
    }
    public String getOperatingEnvironmen() {
        return operatingEnvironmen;
    }

    public void setOperatingEnvironmen(String operatingEnvironmen) {
        this.operatingEnvironmen = operatingEnvironmen;
    }
    public String getCardCaptureCapabilit() {
        return cardCaptureCapabilit;
    }

    public void setCardCaptureCapabilit(String cardCaptureCapabilit) {
        this.cardCaptureCapabilit = cardCaptureCapabilit;
    }
    public String getCardDataInputCapability() {
        return cardDataInputCapability;
    }

    public void setCardDataInputCapability(String cardDataInputCapability) {
        this.cardDataInputCapability = cardDataInputCapability;
    }
    public String getCardDataInputMode() {
        return cardDataInputMode;
    }

    public void setCardDataInputMode(String cardDataInputMode) {
        this.cardDataInputMode = cardDataInputMode;
    }
    public String getCardholderPresenceIndicator() {
        return cardholderPresenceIndicator;
    }

    public void setCardholderPresenceIndicator(String cardholderPresenceIndicator) {
        this.cardholderPresenceIndicator = cardholderPresenceIndicator;
    }
    public String getCardholderAuthenticationEntity() {
        return cardholderAuthenticationEntity;
    }

    public void setCardholderAuthenticationEntity(String cardholderAuthenticationEntity) {
        this.cardholderAuthenticationEntity = cardholderAuthenticationEntity;
    }
    public String getTransactionAmountAccountCurrency() {
        return transactionAmountAccountCurrency;
    }

    public void setTransactionAmountAccountCurrency(String transactionAmountAccountCurrency) {
        this.transactionAmountAccountCurrency = transactionAmountAccountCurrency;
    }
    public String getAcquirerFeeAmount() {
        return acquirerFeeAmount;
    }

    public void setAcquirerFeeAmount(String acquirerFeeAmount) {
        this.acquirerFeeAmount = acquirerFeeAmount;
    }
    public String getFeeSign() {
        return feeSign;
    }

    public void setFeeSign(String feeSign) {
        this.feeSign = feeSign;
    }
    public String getFeeCurrency() {
        return feeCurrency;
    }

    public void setFeeCurrency(String feeCurrency) {
        this.feeCurrency = feeCurrency;
    }
    public String getFeeAmoun() {
        return feeAmoun;
    }

    public void setFeeAmoun(String feeAmoun) {
        this.feeAmoun = feeAmoun;
    }
    public String getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }
    public String getSenderReferenceNumber() {
        return senderReferenceNumber;
    }

    public void setSenderReferenceNumber(String senderReferenceNumber) {
        this.senderReferenceNumber = senderReferenceNumber;
    }
    public String getCustomerMobilePhone() {
        return customerMobilePhone;
    }

    public void setCustomerMobilePhone(String customerMobilePhone) {
        this.customerMobilePhone = customerMobilePhone;
    }
    public String getSvfeTransactionType() {
        return svfeTransactionType;
    }

    public void setSvfeTransactionType(String svfeTransactionType) {
        this.svfeTransactionType = svfeTransactionType;
    }
    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public String getLocalTransactionDateAndTime() {
        return localTransactionDateAndTime;
    }

    public void setLocalTransactionDateAndTime(String localTransactionDateAndTime) {
        this.localTransactionDateAndTime = localTransactionDateAndTime;
    }
    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }
    public String getAcquierInstitueIdentifier() {
        return acquierInstitueIdentifier;
    }

    public void setAcquierInstitueIdentifier(String acquierInstitueIdentifier) {
        this.acquierInstitueIdentifier = acquierInstitueIdentifier;
    }
    public String getAcquirerCountryCode() {
        return acquirerCountryCode;
    }

    public void setAcquirerCountryCode(String acquirerCountryCode) {
        this.acquirerCountryCode = acquirerCountryCode;
    }
    public String getAcquirerNetworkIdentifier() {
        return acquirerNetworkIdentifier;
    }

    public void setAcquirerNetworkIdentifier(String acquirerNetworkIdentifier) {
        this.acquirerNetworkIdentifier = acquirerNetworkIdentifier;
    }
    public String getProcessorCardId() {
        return processorCardId;
    }

    public void setProcessorCardId(String processorCardId) {
        this.processorCardId = processorCardId;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getFeTraceNumber() {
        return feTraceNumber;
    }

    public void setFeTraceNumber(String feTraceNumber) {
        this.feTraceNumber = feTraceNumber;
    }
    public String getFeTransactionDateAndTime() {
        return feTransactionDateAndTime;
    }

    public void setFeTransactionDateAndTime(String feTransactionDateAndTime) {
        this.feTransactionDateAndTime = feTransactionDateAndTime;
    }
    public String getFeTransactionNumber() {
        return feTransactionNumber;
    }

    public void setFeTransactionNumber(String feTransactionNumber) {
        this.feTransactionNumber = feTransactionNumber;
    }
    public String getIssuerNetworkIdentifier() {
        return issuerNetworkIdentifier;
    }

    public void setIssuerNetworkIdentifier(String issuerNetworkIdentifier) {
        this.issuerNetworkIdentifier = issuerNetworkIdentifier;
    }
    public String getNetworkReferenceData() {
        return networkReferenceData;
    }

    public void setNetworkReferenceData(String networkReferenceData) {
        this.networkReferenceData = networkReferenceData;
    }
    public String getTransactionRiskScore() {
        return transactionRiskScore;
    }

    public void setTransactionRiskScore(String transactionRiskScore) {
        this.transactionRiskScore = transactionRiskScore;
    }
    public String getCardAcceptorIdentificationCode() {
        return cardAcceptorIdentificationCode;
    }

    public void setCardAcceptorIdentificationCode(String cardAcceptorIdentificationCode) {
        this.cardAcceptorIdentificationCode = cardAcceptorIdentificationCode;
    }
    public String getTransmissionDateAndTime() {
        return transmissionDateAndTime;
    }

    public void setTransmissionDateAndTime(String transmissionDateAndTime) {
        this.transmissionDateAndTime = transmissionDateAndTime;
    }
    public String getConversionRateAccount() {
        return conversionRateAccount;
    }

    public void setConversionRateAccount(String conversionRateAccount) {
        this.conversionRateAccount = conversionRateAccount;
    }
    public String getAmountCardholderBilling() {
        return amountCardholderBilling;
    }

    public void setAmountCardholderBilling(String amountCardholderBilling) {
        this.amountCardholderBilling = amountCardholderBilling;
    }
    public String getDateTimeLocalTransaction() {
        return dateTimeLocalTransaction;
    }

    public void setDateTimeLocalTransaction(String dateTimeLocalTransaction) {
        this.dateTimeLocalTransaction = dateTimeLocalTransaction;
    }
    public String getAmountTransaction() {
        return amountTransaction;
    }

    public void setAmountTransaction(String amountTransaction) {
        this.amountTransaction = amountTransaction;
    }
    public String getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(String settlementDate) {
        this.settlementDate = settlementDate;
    }
    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }
    public String getCardAcceptorCity() {
        return cardAcceptorCity;
    }

    public void setCardAcceptorCity(String cardAcceptorCity) {
        this.cardAcceptorCity = cardAcceptorCity;
    }
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    public String getAcquiringInstitutionIdentificationCode() {
        return acquiringInstitutionIdentificationCode;
    }

    public void setAcquiringInstitutionIdentificationCode(String acquiringInstitutionIdentificationCode) {
        this.acquiringInstitutionIdentificationCode = acquiringInstitutionIdentificationCode;
    }
    public String getSvfeIssuerInstitutionIdentifier() {
        return svfeIssuerInstitutionIdentifier;
    }

    public void setSvfeIssuerInstitutionIdentifier(String svfeIssuerInstitutionIdentifier) {
        this.svfeIssuerInstitutionIdentifier = svfeIssuerInstitutionIdentifier;
    }
    public String getCardAcceptorTerminalIdentification() {
        return cardAcceptorTerminalIdentification;
    }

    public void setCardAcceptorTerminalIdentification(String cardAcceptorTerminalIdentification) {
        this.cardAcceptorTerminalIdentification = cardAcceptorTerminalIdentification;
    }
    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }
    public Date getAuthStartTime() {
        return authStartTime;
    }

    public void setAuthStartTime(Date authStartTime) {
        this.authStartTime = authStartTime;
    }
    public Date getAuthEndTime() {
        return authEndTime;
    }

    public void setAuthEndTime(Date authEndTime) {
        this.authEndTime = authEndTime;
    }
    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }
    public BigDecimal getTransAmt() {
        return transAmt;
    }

    public void setTransAmt(BigDecimal transAmt) {
        this.transAmt = transAmt;
    }
    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }
    public BigDecimal getBillAmountWithMarkup() {
        return billAmountWithMarkup;
    }

    public void setBillAmountWithMarkup(BigDecimal billAmountWithMarkup) {
        this.billAmountWithMarkup = billAmountWithMarkup;
    }
    public BigDecimal getRemainAuthAmt() {
        return remainAuthAmt;
    }

    public void setRemainAuthAmt(BigDecimal remainAuthAmt) {
        this.remainAuthAmt = remainAuthAmt;
    }
    public BigDecimal getRemainBillAmt() {
        return remainBillAmt;
    }

    public void setRemainBillAmt(BigDecimal remainBillAmt) {
        this.remainBillAmt = remainBillAmt;
    }
    public BigDecimal getRemainBillAmtWithMarkup() {
        return remainBillAmtWithMarkup;
    }

    public void setRemainBillAmtWithMarkup(BigDecimal remainBillAmtWithMarkup) {
        this.remainBillAmtWithMarkup = remainBillAmtWithMarkup;
    }
    public BigDecimal getRemainFrozenAmt() {
        return remainFrozenAmt;
    }

    public void setRemainFrozenAmt(BigDecimal remainFrozenAmt) {
        this.remainFrozenAmt = remainFrozenAmt;
    }
    public String getSystemMark() {
        return systemMark;
    }

    public void setSystemMark(String systemMark) {
        this.systemMark = systemMark;
    }
    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }
    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public Integer getClearStatus() {
        return clearStatus;
    }

    public void setClearStatus(Integer clearStatus) {
        this.clearStatus = clearStatus;
    }
    public String getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }
    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }
    public BigDecimal getReleaseAmt() {
        return releaseAmt;
    }

    public void setReleaseAmt(BigDecimal releaseAmt) {
        this.releaseAmt = releaseAmt;
    }
    public Integer getAuthorizationDecision() {
        return authorizationDecision;
    }

    public void setAuthorizationDecision(Integer authorizationDecision) {
        this.authorizationDecision = authorizationDecision;
    }
    public Date getAuthCompletionTime() {
        return authCompletionTime;
    }

    public void setAuthCompletionTime(Date authCompletionTime) {
        this.authCompletionTime = authCompletionTime;
    }
    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }
    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }
    public String getApproveCode() {
        return approveCode;
    }

    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }
    public Integer getTimeoutFlag() {
        return timeoutFlag;
    }

    public void setTimeoutFlag(Integer timeoutFlag) {
        this.timeoutFlag = timeoutFlag;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public String getOriginalData() {
        return originalData;
    }

    public void setOriginalData(String originalData) {
        this.originalData = originalData;
    }

    @Override
    public String toString() {
        return "KCAuthFlow{" +
            "id=" + id +
            ", channel=" + channel +
            ", requestId=" + requestId +
            ", cardId=" + cardId +
            ", transType=" + transType +
            ", authorizationType=" + authorizationType +
            ", accountingDirection=" + accountingDirection +
            ", referenceNo=" + referenceNo +
            ", transId=" + transId +
            ", originalTransId=" + originalTransId +
            ", originalFinishTime=" + originalFinishTime +
            ", originalFinishDate=" + originalFinishDate +
            ", processingCode=" + processingCode +
            ", primaryAccountNumber=" + primaryAccountNumber +
            ", amountAccount=" + amountAccount +
            ", retrievalReferenceNumber=" + retrievalReferenceNumber +
            ", currencyCodeCardholderBilling=" + currencyCodeCardholderBilling +
            ", currencyCodeTransaction=" + currencyCodeTransaction +
            ", conversionRateCardholderBilling=" + conversionRateCardholderBilling +
            ", systemsTraceAuditNumber=" + systemsTraceAuditNumber +
            ", currencyCodeAccount=" + currencyCodeAccount +
            ", messageType=" + messageType +
            ", accountIdentification=" + accountIdentification +
            ", terminalOutputCapability=" + terminalOutputCapability +
            ", pinCaptureCapability=" + pinCaptureCapability +
            ", cardDataOutputCapability=" + cardDataOutputCapability +
            ", cardholderAuthenticationCapability=" + cardholderAuthenticationCapability +
            ", cardholderAuthenticationMethod=" + cardholderAuthenticationMethod +
            ", cardPresence=" + cardPresence +
            ", operatingEnvironmen=" + operatingEnvironmen +
            ", cardCaptureCapabilit=" + cardCaptureCapabilit +
            ", cardDataInputCapability=" + cardDataInputCapability +
            ", cardDataInputMode=" + cardDataInputMode +
            ", cardholderPresenceIndicator=" + cardholderPresenceIndicator +
            ", cardholderAuthenticationEntity=" + cardholderAuthenticationEntity +
            ", transactionAmountAccountCurrency=" + transactionAmountAccountCurrency +
            ", acquirerFeeAmount=" + acquirerFeeAmount +
            ", feeSign=" + feeSign +
            ", feeCurrency=" + feeCurrency +
            ", feeAmoun=" + feeAmoun +
            ", settlementType=" + settlementType +
            ", senderReferenceNumber=" + senderReferenceNumber +
            ", customerMobilePhone=" + customerMobilePhone +
            ", svfeTransactionType=" + svfeTransactionType +
            ", cardType=" + cardType +
            ", localTransactionDateAndTime=" + localTransactionDateAndTime +
            ", merchantType=" + merchantType +
            ", acquierInstitueIdentifier=" + acquierInstitueIdentifier +
            ", acquirerCountryCode=" + acquirerCountryCode +
            ", acquirerNetworkIdentifier=" + acquirerNetworkIdentifier +
            ", processorCardId=" + processorCardId +
            ", customerId=" + customerId +
            ", feTraceNumber=" + feTraceNumber +
            ", feTransactionDateAndTime=" + feTransactionDateAndTime +
            ", feTransactionNumber=" + feTransactionNumber +
            ", issuerNetworkIdentifier=" + issuerNetworkIdentifier +
            ", networkReferenceData=" + networkReferenceData +
            ", transactionRiskScore=" + transactionRiskScore +
            ", cardAcceptorIdentificationCode=" + cardAcceptorIdentificationCode +
            ", transmissionDateAndTime=" + transmissionDateAndTime +
            ", conversionRateAccount=" + conversionRateAccount +
            ", amountCardholderBilling=" + amountCardholderBilling +
            ", dateTimeLocalTransaction=" + dateTimeLocalTransaction +
            ", amountTransaction=" + amountTransaction +
            ", settlementDate=" + settlementDate +
            ", cardAcceptorName=" + cardAcceptorName +
            ", cardAcceptorCity=" + cardAcceptorCity +
            ", countryCode=" + countryCode +
            ", acquiringInstitutionIdentificationCode=" + acquiringInstitutionIdentificationCode +
            ", svfeIssuerInstitutionIdentifier=" + svfeIssuerInstitutionIdentifier +
            ", cardAcceptorTerminalIdentification=" + cardAcceptorTerminalIdentification +
            ", authType=" + authType +
            ", authStartTime=" + authStartTime +
            ", authEndTime=" + authEndTime +
            ", markupRate=" + markupRate +
            ", transAmt=" + transAmt +
            ", billAmount=" + billAmount +
            ", billAmountWithMarkup=" + billAmountWithMarkup +
            ", remainAuthAmt=" + remainAuthAmt +
            ", remainBillAmt=" + remainBillAmt +
            ", remainBillAmtWithMarkup=" + remainBillAmtWithMarkup +
            ", remainFrozenAmt=" + remainFrozenAmt +
            ", systemMark=" + systemMark +
            ", memberId=" + memberId +
            ", merchantId=" + merchantId +
            ", status=" + status +
            ", clearStatus=" + clearStatus +
            ", releaseStatus=" + releaseStatus +
            ", releaseTime=" + releaseTime +
            ", releaseAmt=" + releaseAmt +
            ", authorizationDecision=" + authorizationDecision +
            ", authCompletionTime=" + authCompletionTime +
            ", returnCode=" + returnCode +
            ", returnMessage=" + returnMessage +
            ", approveCode=" + approveCode +
            ", timeoutFlag=" + timeoutFlag +
            ", createDate=" + createDate +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", originalData=" + originalData +
        "}";
    }
}
