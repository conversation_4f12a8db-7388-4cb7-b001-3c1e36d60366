2025-08-05 08:52:46.519 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [40092: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '40092' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:52:51.529 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached(DnsNameResolver.java:1163)
	at io.netty.resolver.dns.DnsNameResolver.doResolveUncached(DnsNameResolver.java:1065)
	at io.netty.resolver.dns.DnsNameResolver.doResolve(DnsNameResolver.java:1007)
	at io.netty.resolver.dns.DnsNameResolver.doResolve(DnsNameResolver.java:839)
	at io.netty.resolver.SimpleNameResolver.resolve(SimpleNameResolver.java:61)
	at io.netty.resolver.dns.InflightNameResolver.resolve(InflightNameResolver.java:100)
	at io.netty.resolver.dns.InflightNameResolver.resolve(InflightNameResolver.java:66)
	at io.netty.resolver.dns.InflightNameResolver.resolve(InflightNameResolver.java:51)
	at io.netty.resolver.InetSocketAddressResolver.doResolve(InetSocketAddressResolver.java:55)
	at io.netty.resolver.InetSocketAddressResolver.doResolve(InetSocketAddressResolver.java:31)
	at io.netty.resolver.AbstractAddressResolver.resolve(AbstractAddressResolver.java:106)
	at org.redisson.connection.DNSMonitor.monitorMasters(DNSMonitor.java:105)
	at org.redisson.connection.DNSMonitor.access$300(DNSMonitor.java:44)
	at org.redisson.connection.DNSMonitor$1.run(DNSMonitor.java:94)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [31301: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '31301' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached(DnsNameResolver.java:1163)
	at io.netty.resolver.dns.DnsNameResolver.doResolveUncached(DnsNameResolver.java:1065)
	at io.netty.resolver.dns.DnsNameResolver.doResolve(DnsNameResolver.java:1007)
	at io.netty.resolver.dns.DnsNameResolver.doResolve(DnsNameResolver.java:839)
	at io.netty.resolver.SimpleNameResolver.resolve(SimpleNameResolver.java:61)
	at io.netty.resolver.dns.InflightNameResolver.resolve(InflightNameResolver.java:100)
	at io.netty.resolver.dns.InflightNameResolver.resolve(InflightNameResolver.java:66)
	at io.netty.resolver.dns.InflightNameResolver.resolve(InflightNameResolver.java:51)
	at io.netty.resolver.InetSocketAddressResolver.doResolve(InetSocketAddressResolver.java:55)
	at io.netty.resolver.InetSocketAddressResolver.doResolve(InetSocketAddressResolver.java:31)
	at io.netty.resolver.AbstractAddressResolver.resolve(AbstractAddressResolver.java:106)
	at org.redisson.connection.DNSMonitor.monitorMasters(DNSMonitor.java:105)
	at org.redisson.connection.DNSMonitor.access$300(DNSMonitor.java:44)
	at org.redisson.connection.DNSMonitor$1.run(DNSMonitor.java:94)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:52:56.539 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [24575: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '24575' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:53:51.724 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [29576: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '29576' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:53:56.732 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [8913: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '8913' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:01.740 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [58945: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '58945' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:06.751 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [44175: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '44175' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:11.761 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [39804: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '39804' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:16.770 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [55906: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '55906' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:21.781 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [54776: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '54776' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:26.792 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [28635: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '28635' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:31.798 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [44492: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '44492' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 08:54:36.807 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [54047: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '54047' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 09:11:34.580 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [57217: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '57217' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 09:29:00.398 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [29942: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '29942' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 09:44:40.244 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [27854: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '27854' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 10:00:56.581 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [32614: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '32614' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 10:01:26.216 [redisson-netty-2-21] ERROR [  ,  ] org.redisson.connection.DNSMonitor.lambda$monitorMasters$1:108 - Unable to resolve redis.qa.kun
java.net.UnknownHostException: Failed to resolve 'redis.qa.kun' [A(1)] after 2 queries 
	at io.netty.resolver.dns.DnsResolveContext.finishResolve(DnsResolveContext.java:1097)
	at io.netty.resolver.dns.DnsResolveContext.tryToFinishResolve(DnsResolveContext.java:1044)
	at io.netty.resolver.dns.DnsResolveContext.query(DnsResolveContext.java:432)
	at io.netty.resolver.dns.DnsResolveContext.access$700(DnsResolveContext.java:66)
	at io.netty.resolver.dns.DnsResolveContext$2.operationComplete(DnsResolveContext.java:500)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.resolver.dns.DnsQueryContext.finishFailure(DnsQueryContext.java:348)
	at io.netty.resolver.dns.DnsQueryContext.onQueryWriteCompletion(DnsQueryContext.java:276)
	at io.netty.resolver.dns.DnsQueryContext.access$700(DnsQueryContext.java:45)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:267)
	at io.netty.resolver.dns.DnsQueryContext$4.operationComplete(DnsQueryContext.java:264)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.util.internal.PromiseNotificationUtil.tryFailure(PromiseNotificationUtil.java:64)
	at io.netty.channel.ChannelOutboundBuffer.safeFail(ChannelOutboundBuffer.java:732)
	at io.netty.channel.ChannelOutboundBuffer.remove0(ChannelOutboundBuffer.java:317)
	at io.netty.channel.ChannelOutboundBuffer.remove(ChannelOutboundBuffer.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:158)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.resolver.dns.DnsNameResolverException: [3465: /2001:4860:4860:0:0:0:0:8844:53] DefaultDnsQuestion(redis.qa.kun. IN A) failed to send a query '3465' via UDP (no stack trace available)
Caused by: java.net.NoRouteToHostException: No route to host
	at sun.nio.ch.DatagramChannelImpl.send0(Native Method)
	at sun.nio.ch.DatagramChannelImpl.sendFromNativeBuffer(DatagramChannelImpl.java:528)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:490)
	at sun.nio.ch.DatagramChannelImpl.send(DatagramChannelImpl.java:469)
	at io.netty.channel.socket.nio.NioDatagramChannel.doWriteMessage(NioDatagramChannel.java:297)
	at io.netty.channel.nio.AbstractNioMessageChannel.doWrite(AbstractNioMessageChannel.java:143)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush0(AbstractChannel.java:931)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.flush0(AbstractNioChannel.java:354)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.flush(AbstractChannel.java:895)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.flush(DefaultChannelPipeline.java:1372)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush0(AbstractChannelHandlerContext.java:921)
	at io.netty.channel.AbstractChannelHandlerContext.invokeFlush(AbstractChannelHandlerContext.java:907)
	at io.netty.channel.AbstractChannelHandlerContext.flush(AbstractChannelHandlerContext.java:893)
	at io.netty.channel.DefaultChannelPipeline.flush(DefaultChannelPipeline.java:967)
	at io.netty.channel.AbstractChannel.flush(AbstractChannel.java:254)
	at io.netty.resolver.dns.DnsNameResolver.flushQueries(DnsNameResolver.java:1337)
	at io.netty.resolver.dns.DnsResolveContext.internalResolve(DnsResolveContext.java:375)
	at io.netty.resolver.dns.DnsResolveContext.resolve(DnsResolveContext.java:225)
	at io.netty.resolver.dns.DnsNameResolver.resolveNow(DnsNameResolver.java:1235)
	at io.netty.resolver.dns.DnsNameResolver.doResolveAllUncached0(DnsNameResolver.java:1221)
	at io.netty.resolver.dns.DnsNameResolver.access$600(DnsNameResolver.java:95)
	at io.netty.resolver.dns.DnsNameResolver$7.run(DnsNameResolver.java:1169)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 15:23:21.750 [main] ERROR [  ,  ] o.s.b.diagnostics.LoggingFailureAnalysisReporter.report:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 9012 was already in use.

Action:

Identify and stop the process that's listening on port 9012 or configure this application to listen on another port.

2025-08-05 16:07:37.114 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xab5cfe3e, L:/**********:49205 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.117 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x4a4b6871, L:/**********:49226 ! R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.118 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xa76e366e, L:/**********:49204 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.118 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xf98474ec, L:/**********:49207 ! R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.119 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x0165fbe7, L:/**********:49222 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.119 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x97f4a067, L:/**********:49206 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.120 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x86486d49, L:/**********:49224 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.120 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x84a164dc, L:/**********:49210 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.120 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x096b0373, L:/**********:49214 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.121 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xe24493d3, L:/**********:49208 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.124 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xef693dc1, L:/**********:49234 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.126 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xcd4909e6, L:/**********:49231 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.126 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xc166a58d, L:/**********:49221 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.127 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xd366d102, L:/**********:49223 ! R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.127 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x7efd0099, L:/**********:49227 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.128 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x51b6ccad, L:/**********:49215 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.128 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0x5337420c, L:/**********:49228 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.129 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xf016d3dc, L:/**********:49229 ! R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.129 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xd97bb236, L:/**********:49233 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:37.414 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xe5e142dc, L:/**********:49212 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:07:38.114 [redisson-timer-4-1] ERROR [  ,  ] org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1:87 - Unable to send PING command over channel: [id: 0xae17a11d, L:/**********:51485 - R:redis.qa.kun/***********:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://redis.qa.kun:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-05 16:59:37.934 [main] ERROR [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:122 - Started container failed. DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66)
	at java.lang.Runtime.addShutdownHook(Runtime.java:204)
	at org.apache.rocketmq.client.trace.AsyncTraceDispatcher.registerShutDownHook(AsyncTraceDispatcher.java:222)
	at org.apache.rocketmq.client.trace.AsyncTraceDispatcher.start(AsyncTraceDispatcher.java:151)
	at org.apache.rocketmq.client.consumer.DefaultMQPushConsumer.start(DefaultMQPushConsumer.java:701)
	at org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer.start(DefaultRocketMQListenerContainer.java:279)
	at org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration.registerContainer(ListenerContainerConfiguration.java:120)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration.afterSingletonsInstantiated(ListenerContainerConfiguration.java:79)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.kun.linkage.auth.KunLinkageAuthServiceApplication.main(KunLinkageAuthServiceApplication.java:18)
2025-08-05 16:59:38.039 [main] ERROR [  ,  ] org.springframework.boot.SpringApplication.reportFailure:818 - Application run failed
java.lang.RuntimeException: java.lang.IllegalStateException: Shutdown in progress
	at org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration.registerContainer(ListenerContainerConfiguration.java:123)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration.afterSingletonsInstantiated(ListenerContainerConfiguration.java:79)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.kun.linkage.auth.KunLinkageAuthServiceApplication.main(KunLinkageAuthServiceApplication.java:18)
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66)
	at java.lang.Runtime.addShutdownHook(Runtime.java:204)
	at org.apache.rocketmq.client.trace.AsyncTraceDispatcher.registerShutDownHook(AsyncTraceDispatcher.java:222)
	at org.apache.rocketmq.client.trace.AsyncTraceDispatcher.start(AsyncTraceDispatcher.java:151)
	at org.apache.rocketmq.client.consumer.DefaultMQPushConsumer.start(DefaultMQPushConsumer.java:701)
	at org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer.start(DefaultRocketMQListenerContainer.java:279)
	at org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration.registerContainer(ListenerContainerConfiguration.java:120)
	... 12 common frames omitted
