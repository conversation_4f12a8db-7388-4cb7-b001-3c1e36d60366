2025-08-05 17:00:44.005 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 17:00:44.069 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 17:00:44.723 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 17:00:44.723 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 17:00:46.472 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing,DEFAULT_GROUP'}]
2025-08-05 17:00:46.516 [com.alibaba.nacos.client.remote.worker] INFO  [  ,  ] com.alibaba.nacos.common.remote.client.printIfInfoEnabled:60 - [eaca79dc-f980-4f63-bdcc-a338b73d6e16_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-08-05 17:00:46.519 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 17:00:47.736 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 17:00:47.741 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:00:47.779 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-08-05 17:00:47.988 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 17:00:48.333 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=8670b1bc-7a61-3f80-85c9-bf47fa9e3c85
2025-08-05 17:00:48.472 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:48.473 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:48.474 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$542/382697424] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:48.474 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:48.477 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:48.481 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:49.104 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$702f8bcd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:50.035 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 17:00:50.035 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3501 ms
2025-08-05 17:01:01.565 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
