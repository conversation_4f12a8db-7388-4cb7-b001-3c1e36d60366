2025-08-04 10:32:41.592 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 10:32:41.694 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 10:32:42.671 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 10:32:42.671 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 10:32:44.774 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 10:32:44.818 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 10:32:46.010 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 10:32:46.014 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:32:46.048 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-08-04 10:32:46.265 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 10:32:46.586 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=79bb6a58-5c06-3e7f-b96e-d601fda07e91
2025-08-04 10:32:46.718 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.720 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.720 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.721 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.724 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.729 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:47.278 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$22b4d80b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:48.216 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 10:32:48.216 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3381 ms
2025-08-04 10:32:59.033 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 10:33:09.175 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 10:33:10.491 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 10:33:11.494 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 10:33:14.285 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 10:33:15.093 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:8
2025-08-04 10:33:16.266 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 10:33:16.267 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 10:33:16.267 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 10:33:23.391 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 10:33:24.120 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 10:33:24.259 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 10:33:24.370 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 10:33:24.601 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 10:33:25.283 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 10:33:27.041 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 10:33:27.094 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 10:33:27.094 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 10:33:27.105 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 10:33:27.108 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 10:33:27.108 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 10:33:27.108 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 10:33:27.109 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@270b527f
2025-08-04 10:33:30.605 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 10:33:43.385 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 10:33:43.386 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 10:33:54.558 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 10:33:54.559 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 10:34:05.758 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 10:34:05.758 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationTransAccountingReversalListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 10:34:05.878 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 10:34:05.919 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 10:34:05.938 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 10:34:06.025 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 10:34:06.119 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9012 (http)
2025-08-04 10:34:06.156 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 10:34:06.156 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 10:34:06.723 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-auth 172.19.151.145:9012 register finished
2025-08-04 10:34:06.728 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 10:34:06.729 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 10:34:06.758 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStarted:61 - Started KunLinkageAuthServiceApplication in 85.583 seconds (JVM running for 92.44)
2025-08-04 10:34:06.790 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth.properties, group=DEFAULT_GROUP
2025-08-04 10:34:06.790 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth-local.properties, group=DEFAULT_GROUP
2025-08-04 10:34:06.790 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth, group=DEFAULT_GROUP
2025-08-04 10:34:06.867 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 10:34:06.867 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 10:34:06.878 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 10 ms
2025-08-04 10:37:47.825 [XNIO-1 task-2] INFO  [ f0a2ce6bb2ea0926 , f0a2ce6bb2ea0926 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 10:37:48.528 [XNIO-1 task-2] INFO  [ 2e006f69dcb8ecbf , 2e006f69dcb8ecbf ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 656 ms
2025-08-04 10:37:48.582 [XNIO-1 task-2] INFO  [ 2e006f69dcb8ecbf , 2e006f69dcb8ecbf ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 10:43:36.788 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_auth_flow WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-04 10:43:36.788 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 10:43:36.788 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_auth_flow_202505 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202506 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202508 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202509 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202510 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********]
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@83a4ec4], lock=Optional.empty, window=Optional.empty)
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202505 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202506 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202509 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202510 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:38.316 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 47500
2025-08-04 15:07:53.997 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 15:07:54.064 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 15:07:54.748 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:07:54.748 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:07:56.539 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 15:07:56.588 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 15:07:57.824 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:07:57.829 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:07:57.875 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-08-04 15:07:58.243 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 15:07:58.573 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=8dc67e2f-7d9a-3dd5-a833-cfdaeb8f6da9
2025-08-04 15:07:58.706 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:07:58.707 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:07:58.708 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:07:58.708 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:07:58.711 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:07:58.717 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:07:59.233 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$215db80] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:08:00.239 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 15:08:00.239 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3633 ms
2025-08-04 15:08:11.013 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 15:08:20.173 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 15:08:21.500 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 15:08:22.397 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:08:25.837 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:08:26.863 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:14
2025-08-04 15:08:28.313 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 15:08:28.313 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:08:28.313 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 15:08:28.740 [XNIO-1 task-2] INFO  [ ba911afa45956e70 , ba911afa45956e70 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:08:28.788 [XNIO-1 task-2] INFO  [ ab460fbc21320b86 , ab460fbc21320b86 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:08:35.481 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:08:36.046 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 15:08:36.143 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 15:08:36.243 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:08:36.262 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:08:42.577 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:08:42.577 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:08:48.593 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:08:48.594 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:08:48.595 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:08:48.596 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:08:48.596 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:08:48.597 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:08:48.597 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:08:48.597 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:08:48.598 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-04 15:08:48.619 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-04 15:08:48.648 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 15:09:32.964 [XNIO-1 task-2] INFO  [ a889f886ee369f3d , a889f886ee369f3d ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:09:32.991 [XNIO-1 task-2] INFO  [ dc6420dc8d6b4f28 , dc6420dc8d6b4f28 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:10:30.617 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_auth_flow WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-04 15:10:30.619 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:10:30.619 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_auth_flow_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202508 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:10:32.137 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 15:10:32.138 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@58c0a66d], lock=Optional.empty, window=Optional.empty)
2025-08-04 15:10:32.138 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:10:32.139 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:10:32.552 [XNIO-1 task-2] INFO  [ 8a68db1d37784342 , 8a68db1d37784342 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 11119
2025-08-04 15:14:18.938 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 15:14:19.005 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 15:14:19.675 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:14:19.676 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:14:21.434 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 15:14:21.485 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 15:14:22.644 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:14:22.648 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:14:22.676 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-04 15:14:22.864 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 15:14:23.191 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=8dc67e2f-7d9a-3dd5-a833-cfdaeb8f6da9
2025-08-04 15:14:23.313 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:14:23.314 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:14:23.315 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:14:23.315 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:14:23.318 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:14:23.322 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:14:23.819 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$c7a14289] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:14:24.748 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 15:14:24.748 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3246 ms
2025-08-04 15:14:35.846 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 15:14:45.395 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 15:14:46.540 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 15:14:47.458 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:14:50.681 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:14:51.406 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:1
2025-08-04 15:14:52.418 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 15:14:52.418 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:14:52.418 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 15:14:59.312 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:14:59.830 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 15:14:59.938 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 15:15:00.071 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:15:00.090 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:15:06.423 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:15:06.423 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:15:12.437 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:15:12.438 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:15:12.440 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:15:12.441 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:15:12.441 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:15:12.442 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:15:12.442 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:15:12.442 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:15:12.444 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-04 15:15:12.456 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-04 15:15:12.488 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 15:17:07.227 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 15:17:07.291 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 15:17:07.943 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:17:07.943 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:17:09.693 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 15:17:09.728 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 15:17:10.864 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:17:10.869 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:17:10.898 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-04 15:17:11.095 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 15:17:11.417 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=8dc67e2f-7d9a-3dd5-a833-cfdaeb8f6da9
2025-08-04 15:17:11.538 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:17:11.539 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:17:11.539 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:17:11.540 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:17:11.543 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:17:11.547 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:17:12.035 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$c7a14289] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:17:12.952 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 15:17:12.953 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3210 ms
2025-08-04 15:17:24.061 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 15:17:33.594 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 15:17:34.733 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 15:17:35.610 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:17:39.167 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:17:39.874 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:2
2025-08-04 15:17:40.890 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 15:17:40.890 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:17:40.891 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 15:17:47.733 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:17:48.274 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 15:17:48.380 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 15:17:48.489 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:17:48.512 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:17:54.840 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:17:54.842 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:18:00.852 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:18:00.852 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:18:00.853 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:18:00.854 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:18:00.854 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:18:00.854 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:18:00.854 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:18:00.855 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:18:00.856 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-04 15:18:00.868 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-04 15:18:00.898 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 15:22:33.516 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 15:22:33.580 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 15:22:34.238 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:22:34.238 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:22:36.022 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 15:22:36.074 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 15:22:37.231 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:22:37.235 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:22:37.264 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-04 15:22:37.467 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 15:22:37.805 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=3c6093b9-735f-3df2-87dd-a155903fd659
2025-08-04 15:22:37.934 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:22:37.935 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:22:37.935 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/501693751] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:22:37.936 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:22:37.938 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:22:37.943 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:22:38.452 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$dfa11eb1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:22:39.349 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 15:22:39.349 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3259 ms
2025-08-04 15:22:50.059 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 15:22:59.032 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 15:23:00.175 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 15:23:01.075 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:23:03.922 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:23:04.607 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:3
2025-08-04 15:23:05.571 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 15:23:05.571 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:23:05.571 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 15:23:12.412 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:23:12.934 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 15:23:13.049 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 15:23:13.166 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:23:13.185 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:23:20.051 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:23:20.052 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:23:26.063 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:23:26.064 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:23:26.065 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:23:26.066 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:23:26.067 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:23:26.067 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:23:26.067 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:23:26.067 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:23:26.069 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-04 15:23:26.081 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-04 15:23:26.111 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 15:23:59.557 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 15:23:59.626 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 15:24:00.287 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:24:00.287 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:24:02.060 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 15:24:02.109 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 15:24:03.293 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:24:03.297 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:24:03.327 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-04 15:24:03.516 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 15:24:03.844 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=eccd2cc8-fb11-32d7-8dfe-58e7c84d8376
2025-08-04 15:24:03.965 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:24:03.966 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:24:03.966 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/191953464] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:24:03.966 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:24:03.969 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:24:03.973 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:24:04.461 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$c8d308a4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:24:05.374 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 15:24:05.374 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3248 ms
2025-08-04 15:24:15.798 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 15:24:24.486 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 15:24:25.650 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 15:24:26.537 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:24:29.166 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:24:29.858 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:4
2025-08-04 15:24:30.856 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 15:24:30.856 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:24:30.856 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 15:24:37.758 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:24:38.275 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 15:24:38.370 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 15:24:38.470 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:24:38.489 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:24:39.649 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 15:24:40.989 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 15:24:41.037 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 15:24:41.037 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 15:24:41.048 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 15:24:41.050 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 15:24:41.050 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 15:24:41.050 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 15:24:41.051 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@51b274ce
2025-08-04 15:24:44.253 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 15:24:56.817 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:24:56.817 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 15:25:05.091 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_auth_flow WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-04 15:25:05.092 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:25:05.093 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_auth_flow_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202508 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:25:05.286 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 15:25:05.286 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@58c0a66d], lock=Optional.empty, window=Optional.empty)
2025-08-04 15:25:05.287 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:25:05.287 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:25:05.496 [XNIO-1 task-2] INFO  [ 69bacfbd52ce843d , 69bacfbd52ce843d ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 11119
2025-08-04 15:25:07.991 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:25:08.003 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 15:25:12.349 [XNIO-1 task-2] INFO  [ a41aac218ed0e1ff , a41aac218ed0e1ff ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:25:12.372 [XNIO-1 task-2] INFO  [ d048d42667fe1c1f , d048d42667fe1c1f ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:25:19.199 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:25:19.200 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationTransAccountingReversalConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 15:25:19.351 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 15:25:19.388 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 15:25:19.405 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 15:25:19.487 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 15:25:19.583 [main] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-08-04 15:25:25.596 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:25:25.871 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_auth_flow WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-04 15:25:25.872 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:25:25.872 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_auth_flow_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202508 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:25:25.951 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 15:25:25.952 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@58c0a66d], lock=Optional.empty, window=Optional.empty)
2025-08-04 15:25:25.953 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:25:25.953 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:25:26.156 [XNIO-1 task-2] INFO  [ d5f98ecd5f25736a , d5f98ecd5f25736a ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 11119
2025-08-04 15:25:32.050 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:25:38.059 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:25:38.092 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-08-04 15:25:38.092 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-04 15:25:38.092 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 15:25:38.093 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-04 15:25:44.265 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:25:44.266 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:25:50.276 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 15:25:50.277 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 15:25:50.279 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:25:50.280 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:25:50.281 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:25:50.281 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-04 15:25:50.281 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-04 15:25:50.281 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-04 15:25:50.284 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-04 15:25:50.297 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-04 15:25:50.329 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 15:26:07.057 [XNIO-1 task-2] INFO  [ d91ec3cc95f6cc5a , d91ec3cc95f6cc5a ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:26:07.079 [XNIO-1 task-2] INFO  [ a57f527ad7284dbf , a57f527ad7284dbf ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:26:07.756 [XNIO-1 task-2] INFO  [ aaafc8e21e539299 , aaafc8e21e539299 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:26:07.776 [XNIO-1 task-2] INFO  [ ecd5d7e3b99f63ff , ecd5d7e3b99f63ff ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:26:08.099 [XNIO-1 task-2] INFO  [ 16437fa368bf7a51 , 16437fa368bf7a51 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:26:08.115 [XNIO-1 task-2] INFO  [ 1d929b6bc3732efd , 1d929b6bc3732efd ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:26:08.267 [XNIO-1 task-2] INFO  [ 4dede87f99f093e7 , 4dede87f99f093e7 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:26:08.282 [XNIO-1 task-2] INFO  [ 53402a09b4a17891 , 53402a09b4a17891 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:26:08.445 [XNIO-1 task-2] INFO  [ 7e1d9677c5fdcf08 , 7e1d9677c5fdcf08 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:26:08.457 [XNIO-1 task-2] INFO  [ 637cd7ad36f1b778 , 637cd7ad36f1b778 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:26:08.628 [XNIO-1 task-2] INFO  [ 04623a8d350a579a , 04623a8d350a579a ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:26:08.642 [XNIO-1 task-2] INFO  [ 9bb7f96847892176 , 9bb7f96847892176 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 15:26:46.616 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 15:26:46.681 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 15:26:47.338 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:26:47.339 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:26:49.106 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 15:26:49.159 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 15:26:50.314 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:26:50.318 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:26:50.347 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-04 15:26:50.537 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 15:26:50.866 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=eccd2cc8-fb11-32d7-8dfe-58e7c84d8376
2025-08-04 15:26:50.987 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:26:50.988 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:26:50.988 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/191953464] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:26:50.989 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:26:50.991 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:26:50.995 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:26:51.487 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$c8d308a4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:26:52.396 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 15:26:52.397 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3221 ms
2025-08-04 15:27:02.886 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 15:27:12.090 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 15:27:13.223 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 15:27:14.160 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:27:17.238 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:27:17.973 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:5
2025-08-04 15:27:18.974 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 15:27:18.974 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:27:18.974 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 15:27:25.802 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:27:26.335 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 15:27:26.433 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 15:27:26.538 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:27:26.561 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:27:27.743 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 15:27:29.078 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 15:27:29.127 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 15:27:29.127 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 15:27:29.138 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 15:27:29.140 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 15:27:29.140 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 15:27:29.140 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 15:27:29.140 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@8b62702
2025-08-04 15:27:32.229 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 15:27:44.735 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:27:44.736 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 15:27:55.925 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:27:55.927 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 15:28:07.107 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:28:07.108 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationTransAccountingReversalConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 15:28:07.251 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 15:28:07.288 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 15:28:07.305 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 15:28:07.388 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 15:28:07.483 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9013 (http)
2025-08-04 15:28:07.522 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:28:07.523 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:28:08.082 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-auth 172.19.151.145:9013 register finished
2025-08-04 15:28:08.087 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 15:28:08.087 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 15:28:08.118 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStarted:61 - Started KunLinkageAuthServiceApplication in 81.816 seconds (JVM running for 88.044)
2025-08-04 15:28:08.153 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth.properties, group=DEFAULT_GROUP
2025-08-04 15:28:08.153 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth-local.properties, group=DEFAULT_GROUP
2025-08-04 15:28:08.153 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth, group=DEFAULT_GROUP
2025-08-04 15:28:08.586 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 15:28:08.587 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 15:28:08.593 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 6 ms
2025-08-04 15:28:29.320 [XNIO-1 task-31] INFO  [ dffdcef012f0aff9 , dffdcef012f0aff9 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 15:28:30.074 [XNIO-1 task-31] INFO  [ 1ce56308de8b4554 , 1ce56308de8b4554 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 703 ms
2025-08-04 15:28:30.100 [XNIO-1 task-31] INFO  [ 1ce56308de8b4554 , 1ce56308de8b4554 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 36696
2025-08-04 15:28:47.413 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_auth_flow WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-04 15:28:47.413 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:28:47.413 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_auth_flow_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202508 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:28:47.808 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 15:28:47.808 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@7ec24a9a], lock=Optional.empty, window=Optional.empty)
2025-08-04 15:28:47.809 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:28:47.809 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:28:48.180 [XNIO-1 task-31] INFO  [ a9fc29eb940bc8c5 , a9fc29eb940bc8c5 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 11119
2025-08-04 15:28:59.860 [XNIO-1 task-31] INFO  [ **************** , **************** ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  ( ?,
?,
?,
?,


?,

?,
? )
2025-08-04 15:28:59.860 [XNIO-1 task-31] INFO  [ **************** , **************** ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-08-04 15:28:59.861 [XNIO-1 task-31] INFO  [ **************** , **************** ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  (?, ?, ?, ?, ?, ?, ?) ::: [1952270527339470849, ********, Authorization_********_20250804152859.csv, AUTHORIZATION_EXPORT, PROCESSING, 2025-08-04 15:28:59.0, 2025-08-04 15:28:59.0]
2025-08-04 15:29:00.160 [XNIO-1 task-31] INFO  [ **************** , **************** ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/asyncExport | Status: 200 | Body: {"code":"KLC000... | Total Length: 65
2025-08-04 15:30:44.833 [XNIO-1 task-31] INFO  [ e9a24bbfd8418be2 , e9a24bbfd8418be2 ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  ( ?,
?,
?,
?,


?,

?,
? )
2025-08-04 15:30:44.834 [XNIO-1 task-31] INFO  [ e9a24bbfd8418be2 , e9a24bbfd8418be2 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-08-04 15:30:44.834 [XNIO-1 task-31] INFO  [ e9a24bbfd8418be2 , e9a24bbfd8418be2 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  (?, ?, ?, ?, ?, ?, ?) ::: [1952270967816888321, ********, Authorization_********_20250804153044.csv, AUTHORIZATION_EXPORT, PROCESSING, 2025-08-04 15:30:44.0, 2025-08-04 15:30:44.0]
2025-08-04 15:30:45.130 [XNIO-1 task-31] INFO  [ e9a24bbfd8418be2 , e9a24bbfd8418be2 ] c.kun.linkage.auth.service.ExportFileRecordService.createFileRecord:45 - 创建文件记录成功，文件记录ID: 1952270967816888321, 文件名: Authorization_********_20250804153044.csv
2025-08-04 15:30:45.147 [XNIO-1 task-31] INFO  [ e9a24bbfd8418be2 , e9a24bbfd8418be2 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/asyncExport | Status: 200 | Body: {"code":"0000",... | Total Length: 64
2025-08-04 15:30:45.167 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:64 - 开始异步导出授权记录数据，文件记录ID: 1952270967816888321
2025-08-04 15:30:45.180 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
2025-08-04 15:30:45.181 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:30:45.181 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:30:45.181 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:30:45.466 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:68 - 查询到 13 条授权记录数据
2025-08-04 15:30:45.498 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:72 - CSV文件生成完成，文件大小: 2394 bytes
2025-08-04 15:30:48.570 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:76 - 文件上传S3成功，URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/authorization-export/Authorization_********_20250804153044.csv
2025-08-04 15:30:48.608 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=?
2025-08-04 15:30:48.608 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-08-04 15:30:48.608 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=? ::: [2394, https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/authorization-export/Authorization_********_20250804153044.csv, SUCCESS, 2025-08-04 15:30:48.0, 1952270967816888321]
2025-08-04 15:30:48.777 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] c.kun.linkage.auth.service.ExportFileRecordService.updateFileRecordSuccess:61 - 更新文件记录为成功状态，文件记录ID: 1952270967816888321, S3 URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/authorization-export/Authorization_********_20250804153044.csv
2025-08-04 15:30:48.778 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:84 - 临时文件已清理: /var/folders/s_/n4rjfkbj7s13rzhsr4yxs34c0000gp/T/auth_export_6970790202633876606.csv
2025-08-04 15:30:48.778 [ExternalAPI-1] INFO  [ e9a24bbfd8418be2 , 9552d156738dec21 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:86 - 授权记录数据导出完成，文件记录ID: 1952270967816888321
2025-08-04 15:39:02.368 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 15:40:28.498 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 15:40:28.584 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 15:40:29.316 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:40:29.317 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:40:31.130 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 15:40:31.179 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 15:40:32.474 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:40:32.478 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:40:32.509 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-04 15:40:32.704 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 15:40:33.032 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=eccd2cc8-fb11-32d7-8dfe-58e7c84d8376
2025-08-04 15:40:33.156 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:40:33.157 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:40:33.158 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/191953464] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:40:33.158 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:40:33.161 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:40:33.165 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:40:33.676 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$c8d308a4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:40:34.689 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 15:40:34.690 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3494 ms
2025-08-04 15:40:47.074 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 15:40:56.424 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 15:40:57.655 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 15:40:58.701 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:41:01.918 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 15:41:02.609 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:6
2025-08-04 15:41:03.633 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 15:41:03.634 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:41:03.634 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 15:41:10.697 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:41:11.265 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 15:41:11.365 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 15:41:11.467 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:41:11.486 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 15:41:12.812 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 15:41:14.211 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 15:41:14.261 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 15:41:14.261 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 15:41:14.272 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 15:41:14.275 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 15:41:14.275 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 15:41:14.275 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 15:41:14.275 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@75fe135a
2025-08-04 15:41:17.506 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 15:41:30.260 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:41:30.261 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 15:41:41.442 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:41:41.443 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 15:41:52.611 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 15:41:52.612 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationTransAccountingReversalConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 15:41:52.760 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 15:41:52.829 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 15:41:52.851 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 15:41:52.933 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 15:41:53.034 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9013 (http)
2025-08-04 15:41:53.082 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 15:41:53.082 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 15:41:53.659 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-auth 172.19.151.145:9013 register finished
2025-08-04 15:41:53.664 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 15:41:53.665 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 15:41:53.692 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStarted:61 - Started KunLinkageAuthServiceApplication in 85.536 seconds (JVM running for 91.779)
2025-08-04 15:41:53.724 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth.properties, group=DEFAULT_GROUP
2025-08-04 15:41:53.725 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth-local.properties, group=DEFAULT_GROUP
2025-08-04 15:41:53.725 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth, group=DEFAULT_GROUP
2025-08-04 15:41:54.259 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 15:41:54.259 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 15:41:54.266 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 7 ms
2025-08-04 15:42:02.697 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_auth_flow WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-04 15:42:02.698 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:42:02.698 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_auth_flow_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202508 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:42:03.106 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 15:42:03.107 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@********], lock=Optional.empty, window=Optional.empty)
2025-08-04 15:42:03.107 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:42:03.107 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********, 100]
2025-08-04 15:42:03.468 [XNIO-1 task-1] INFO  [ d0f3174a4d91e469 , d0f3174a4d91e469 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 11119
2025-08-04 15:42:16.347 [XNIO-1 task-1] INFO  [ 17127ca830167275 , 17127ca830167275 ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  ( ?,
?,
?,
?,


?,

?,
? )
2025-08-04 15:42:16.347 [XNIO-1 task-1] INFO  [ 17127ca830167275 , 17127ca830167275 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-08-04 15:42:16.347 [XNIO-1 task-1] INFO  [ 17127ca830167275 , 17127ca830167275 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  (?, ?, ?, ?, ?, ?, ?) ::: [1952273868110970882, ********, Authorization_********_20250804154216.csv, AUTHORIZATION_EXPORT, PROCESSING, 2025-08-04 15:42:16.0, 2025-08-04 15:42:16.0]
2025-08-04 15:42:16.507 [XNIO-1 task-1] INFO  [ 17127ca830167275 , 17127ca830167275 ] c.kun.linkage.auth.service.ExportFileRecordService.createFileRecord:45 - 创建文件记录成功，文件记录ID: 1952273868110970882, 文件名: Authorization_********_20250804154216.csv
2025-08-04 15:42:16.528 [XNIO-1 task-1] INFO  [ 17127ca830167275 , 17127ca830167275 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/asyncExport | Status: 200 | Body: {"code":"0000",... | Total Length: 64
2025-08-04 15:42:16.555 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:64 - 开始异步导出授权记录数据，文件记录ID: 1952273868110970882
2025-08-04 15:42:16.570 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
2025-08-04 15:42:16.570 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:42:16.571 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:42:16.571 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC ::: [2025-07-01 00:00:00.0, 2025-08-01 23:59:59.0, ********]
2025-08-04 15:42:16.875 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:68 - 查询到 13 条授权记录数据
2025-08-04 15:42:16.915 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:72 - CSV文件生成完成，文件大小: 2550 bytes
2025-08-04 15:42:19.639 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:76 - 文件上传S3成功，URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/authorization-export/Authorization_********_20250804154216.csv
2025-08-04 15:42:19.674 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=?
2025-08-04 15:42:19.674 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-08-04 15:42:19.674 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=? ::: [2550, https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/authorization-export/Authorization_********_20250804154216.csv, SUCCESS, 2025-08-04 15:42:19.0, 1952273868110970882]
2025-08-04 15:42:19.827 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] c.kun.linkage.auth.service.ExportFileRecordService.updateFileRecordSuccess:61 - 更新文件记录为成功状态，文件记录ID: 1952273868110970882, S3 URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/authorization-export/Authorization_********_20250804154216.csv
2025-08-04 15:42:19.828 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:84 - 临时文件已清理: /var/folders/s_/n4rjfkbj7s13rzhsr4yxs34c0000gp/T/auth_export_8131986478138908688.csv
2025-08-04 15:42:19.829 [ExternalAPI-1] INFO  [ 17127ca830167275 , 19e76ab5ac9df5c1 ] c.k.l.auth.service.AuthorizationExportService.asyncExportData:86 - 授权记录数据导出完成，文件记录ID: 1952273868110970882
2025-08-04 15:44:12.916 [XNIO-1 task-1] INFO  [ e425f71d52b98d8a , e425f71d52b98d8a ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=? 
2025-08-04 15:44:12.917 [XNIO-1 task-1] INFO  [ e425f71d52b98d8a , e425f71d52b98d8a ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:44:12.918 [XNIO-1 task-1] INFO  [ e425f71d52b98d8a , e425f71d52b98d8a ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=?  ::: [1952270967816888321]
2025-08-04 15:44:13.085 [XNIO-1 task-1] INFO  [ e425f71d52b98d8a , e425f71d52b98d8a ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/org/exportFileRecord/downloadFile/1952270967816888321 | Status: 200 | Body: {"code":"0000",... | Total Length: 174
2025-08-04 15:44:42.020 [XNIO-1 task-1] INFO  [ f8d4bf55d1e078ac , f8d4bf55d1e078ac ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_export_file_record
2025-08-04 15:44:42.021 [XNIO-1 task-1] INFO  [ f8d4bf55d1e078ac , f8d4bf55d1e078ac ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 15:44:42.021 [XNIO-1 task-1] INFO  [ f8d4bf55d1e078ac , f8d4bf55d1e078ac ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_export_file_record
2025-08-04 15:44:42.110 [XNIO-1 task-1] INFO  [ f8d4bf55d1e078ac , f8d4bf55d1e078ac ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time  FROM kl_export_file_record 
 
  
 
  ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 15:44:42.111 [XNIO-1 task-1] INFO  [ f8d4bf55d1e078ac , f8d4bf55d1e078ac ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@4062ff6c], lock=Optional.empty, window=Optional.empty)
2025-08-04 15:44:42.111 [XNIO-1 task-1] INFO  [ f8d4bf55d1e078ac , f8d4bf55d1e078ac ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time  FROM kl_export_file_record 
 
  
 
  ORDER BY create_time DESC
 LIMIT ?  ::: [100]
2025-08-04 15:44:42.206 [XNIO-1 task-1] INFO  [ f8d4bf55d1e078ac , f8d4bf55d1e078ac ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/exportFileRecord/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 950
2025-08-04 15:49:42.636 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 20:58:38.086 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:38.089 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:48.701 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:48.709 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:49.697 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:49.704 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:58.101 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:58.106 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:58.109 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:58.111 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:58.114 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 20:58:58.119 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.131 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.133 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.135 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.136 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.138 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.139 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.697 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:54.706 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:55.694 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:00:55.704 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:31.496 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:31.500 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:31.502 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:31.505 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:31.508 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:31.510 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:42.051 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:42.055 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:43.041 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:14:43.044 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:31:34.532 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:31:34.534 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:31:34.536 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:31:34.537 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:31:34.540 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 21:31:34.543 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:15.478 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:15.939 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:15.953 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:18.556 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:18.557 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:18.559 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:19.944 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:21.565 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:44.815 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-04 22:10:47.818 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
