2025-08-05 10:39:18.921 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 10:39:19.003 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 10:39:19.739 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 10:39:19.739 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 10:39:21.526 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 10:39:21.576 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 10:39:23.232 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 10:39:23.237 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 10:39:23.277 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-05 10:39:23.524 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 10:39:23.869 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=2c89e32c-c13d-3423-8367-377a43671463
2025-08-05 10:39:24.005 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:39:24.006 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:39:24.006 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:39:24.007 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:39:24.010 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:39:24.015 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:39:24.694 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$7d5aa4e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:39:25.680 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 10:39:25.680 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 4083 ms
2025-08-05 10:39:38.714 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 10:39:47.356 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:39:47.459 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:39:47.475 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:39:48.373 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 10:39:48.373 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 10:39:48.373 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 10:39:55.115 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:39:55.151 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:39:55.870 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 10:39:57.854 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 10:40:02.429 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 10:40:09.688 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:40:10.389 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 10:40:10.432 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-05 10:40:10.855 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 10:40:12.587 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 10:40:12.641 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 10:40:12.641 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 10:40:12.652 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 10:40:12.655 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 10:40:12.656 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 10:40:12.656 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 10:40:12.656 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@b627ffa
2025-08-05 10:40:15.846 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-05 10:40:16.634 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5b481d77[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$42974815#mpcWalletEventRetryNotifyTask]
2025-08-05 10:40:16.634 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@761a429[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$cce523a5#organizationFeeMonthlyReportTask]
2025-08-05 10:40:16.635 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@411d3b5e[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$93e23a97#organizationSMSFeeCalculateTask]
2025-08-05 10:40:16.635 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@53de637a[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$2034ced0#syncCustomerInfoTask]
2025-08-05 10:40:16.649 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:40:16.668 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:40:22.036 [Thread-153] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-05 10:40:28.492 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:40:28.493 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 10:40:39.739 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:40:39.740 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 10:40:50.974 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:40:50.975 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-05 10:41:02.205 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:41:02.206 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-05 10:41:10.460 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:41:10.461 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-05 10:41:10.604 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-05 10:41:10.645 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-05 10:41:10.664 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-05 10:41:10.757 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-05 10:41:10.871 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8081 (http)
2025-08-05 10:41:10.926 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 10:41:10.926 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 10:41:11.472 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8081 register finished
2025-08-05 10:41:11.475 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-05 10:41:11.475 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-05 10:41:11.512 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 113.031 seconds (JVM running for 119.634)
2025-08-05 10:41:11.552 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-05 10:41:11.553 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-05 10:41:11.553 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-05 10:41:12.094 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 10:41:12.094 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 10:41:12.101 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 7 ms
2025-08-05 10:48:20.462 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-05 10:48:28.950 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 10:48:29.014 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 10:48:29.606 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 10:48:29.607 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 10:48:31.312 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 10:48:31.359 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 10:48:32.605 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 10:48:32.610 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 10:48:32.658 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-08-05 10:48:32.863 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 10:48:33.212 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=2c89e32c-c13d-3423-8367-377a43671463
2025-08-05 10:48:33.334 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:48:33.335 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:48:33.335 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:48:33.336 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:48:33.339 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:48:33.343 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:48:33.957 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$22071bdc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 10:48:34.847 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 10:48:34.847 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3470 ms
2025-08-05 10:48:48.341 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 10:48:56.533 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:48:56.627 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:48:56.643 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:48:57.479 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 10:48:57.479 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 10:48:57.479 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 10:49:04.053 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:49:04.088 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:49:04.710 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 10:49:06.037 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 10:49:10.015 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 10:49:17.035 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:49:17.686 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 10:49:17.733 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-05 10:49:18.171 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 10:49:19.801 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 10:49:19.850 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 10:49:19.850 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 10:49:19.861 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 10:49:19.864 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 10:49:19.864 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 10:49:19.864 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 10:49:19.864 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@66752456
2025-08-05 10:49:23.101 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-05 10:49:23.899 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@379445f[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$b8e1e1bc#mpcWalletEventRetryNotifyTask]
2025-08-05 10:49:23.899 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1f7e6c3a[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$432fbd4c#organizationFeeMonthlyReportTask]
2025-08-05 10:49:23.900 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@55024328[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$a2cd43e#organizationSMSFeeCalculateTask]
2025-08-05 10:49:23.900 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@40190f7f[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$967f6877#syncCustomerInfoTask]
2025-08-05 10:49:23.914 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:49:23.933 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 10:49:29.327 [Thread-150] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-05 10:49:35.652 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:49:35.652 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 10:49:46.881 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:49:46.882 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 10:49:58.107 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:49:58.108 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-05 10:50:09.340 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:50:09.341 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-05 10:50:17.565 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 10:50:17.566 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-05 10:50:17.687 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-05 10:50:17.728 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-05 10:50:17.748 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-05 10:50:17.845 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-05 10:50:17.944 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-05 10:50:17.984 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 10:50:17.984 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 10:50:18.583 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-05 10:50:18.589 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-05 10:50:18.589 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-05 10:50:18.648 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 110.016 seconds (JVM running for 116.634)
2025-08-05 10:50:18.683 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-05 10:50:18.683 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-05 10:50:18.683 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-05 10:50:19.075 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 10:50:19.075 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 10:50:19.086 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 11 ms
2025-08-05 10:52:01.779 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  ( ?,
?,
?,
?,


?,

?,
? )
2025-08-05 10:52:01.780 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-08-05 10:52:01.780 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  (?, ?, ?, ?, ?, ?, ?) ::: [1952563213741182977, 12090276, Topup_12090276_20250805105201.csv, CARD_RECHARGE_EXPORT, PROCESSING, 2025-08-05 10:52:01.0, 2025-08-05 10:52:01.0]
2025-08-05 10:52:02.206 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.ExportFileRecordService.createFileRecord:35 - 创建文件记录成功，文件记录ID: 1952563213741182977, 文件名: Topup_12090276_20250805105201.csv
2025-08-05 10:52:02.206 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:84 - 开始异步导出卡充值记录数据，文件记录ID: 1952563213741182977
2025-08-05 10:52:02.427 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?) ORDER BY recharge_datetime DESC
2025-08-05 10:52:02.427 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 10:52:02.427 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?) ORDER BY recharge_datetime DESC ::: [12090276, 23, 2025-07-01T00:00, 2025-08-01T23:59:59]
2025-08-05 10:52:02.715 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:88 - 查询到 10 条卡充值记录数据
2025-08-05 10:52:02.745 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:92 - CSV文件生成完成，文件大小: 1839 bytes
2025-08-05 10:52:06.267 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:96 - 文件上传S3成功，URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/cardRecharge/202508/Topup_12090276_20250805105201.csv
2025-08-05 10:52:06.294 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=?
2025-08-05 10:52:06.294 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-08-05 10:52:06.294 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=? ::: [1839, https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/cardRecharge/202508/Topup_12090276_20250805105201.csv, SUCCESS, 2025-08-05 10:52:06.0, 1952563213741182977]
2025-08-05 10:52:06.529 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.ExportFileRecordService.updateFileRecordSuccess:51 - 更新文件记录为成功状态，文件记录ID: 1952563213741182977, S3 URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/cardRecharge/202508/Topup_12090276_20250805105201.csv
2025-08-05 10:52:06.530 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:100 - 卡充值记录导出任务完成，文件记录ID: 1952563213741182977
2025-08-05 10:52:06.530 [XNIO-1 task-1] INFO  [ b0cadcb588d7fe26 , 57c5801dcb8173c3 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:105 - 临时文件删除成功: /var/folders/s_/n4rjfkbj7s13rzhsr4yxs34c0000gp/T/recharge_export_7398075231858908324.csv
2025-08-05 10:52:25.412 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-05 16:59:57.075 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 16:59:57.141 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 16:59:57.751 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 16:59:57.751 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 16:59:59.496 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 16:59:59.547 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 17:00:00.713 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 17:00:00.717 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:00:00.755 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-05 17:00:00.962 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 17:00:01.279 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=4582b2dc-7b87-3550-9eba-780026b60554
2025-08-05 17:00:01.403 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:01.404 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:01.404 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:01.405 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:01.408 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:01.412 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:02.013 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$9e88631f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:00:02.911 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 17:00:02.911 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3344 ms
2025-08-05 17:00:14.334 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 17:00:21.458 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:00:21.562 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:00:21.577 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:28:42.297 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 17:28:42.366 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 17:28:43.143 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 17:28:43.144 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 17:28:44.939 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 17:28:44.986 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 17:28:46.659 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 17:28:46.666 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:28:46.707 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-08-05 17:28:46.956 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 17:28:47.358 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=7fb93919-1fb3-3bfd-a6a8-27ae86e3a07e
2025-08-05 17:28:47.495 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:28:47.496 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:28:47.497 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:28:47.497 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:28:47.500 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:28:47.505 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:28:48.187 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4b75073a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:28:49.172 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 17:28:49.172 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 4166 ms
2025-08-05 17:29:00.835 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 17:29:13.510 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:13.605 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:13.619 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:14.468 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 17:29:14.469 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:29:14.469 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 17:29:21.137 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:21.170 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:21.776 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 17:29:23.666 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 17:29:28.306 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 17:29:35.381 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:35.786 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:36 - 初始化 AmazonS3 客户端，region: ap-east-1, bucket: qa-aws-static-s3
2025-08-05 17:29:35.786 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:51 - 使用自定义 S3 endpoint: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com
2025-08-05 17:29:35.791 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:64 - AmazonS3 客户端初始化完成
2025-08-05 17:29:36.087 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 17:29:36.130 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-05 17:29:36.525 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 17:29:38.131 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 17:29:38.178 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 17:29:38.178 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 17:29:38.188 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 17:29:38.190 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 17:29:38.190 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 17:29:38.191 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 17:29:38.191 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@b939971
2025-08-05 17:29:42.681 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-05 17:29:43.570 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2f930be7[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$ff4493ce#mpcWalletEventRetryNotifyTask]
2025-08-05 17:29:43.571 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@cfc9c4f[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$89926f5e#organizationFeeMonthlyReportTask]
2025-08-05 17:29:43.571 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4f241437[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$508f8650#organizationSMSFeeCalculateTask]
2025-08-05 17:29:43.572 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@35697f9e[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$dce21a89#syncCustomerInfoTask]
2025-08-05 17:29:43.587 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:43.607 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:29:48.987 [Thread-161] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-05 17:29:55.295 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:29:55.296 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 17:30:06.467 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:30:06.467 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 17:30:17.667 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:30:17.668 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-05 17:30:28.890 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:30:28.892 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-05 17:30:37.727 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:30:37.729 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-05 17:30:37.865 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-05 17:30:37.905 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-05 17:30:37.922 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-05 17:30:38.008 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-05 17:30:38.100 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-05 17:30:38.138 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 17:30:38.138 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 17:30:38.721 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-05 17:30:38.727 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-05 17:30:38.727 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-05 17:30:38.769 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 116.818 seconds (JVM running for 123.187)
2025-08-05 17:30:38.800 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-05 17:30:38.800 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-05 17:30:38.801 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-05 17:30:39.021 [RMI TCP Connection(3)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 17:30:39.021 [RMI TCP Connection(3)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 17:30:39.027 [RMI TCP Connection(3)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 6 ms
2025-08-05 17:31:02.106 [XNIO-1 task-1] INFO  [ 783caa5f36e9f51a , 783caa5f36e9f51a ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1086 ms
2025-08-05 17:32:02.344 [XNIO-1 task-1] INFO  [ bcf69672f13ed80c , bcf69672f13ed80c ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-05 17:32:02.344 [XNIO-1 task-1] INFO  [ bcf69672f13ed80c , bcf69672f13ed80c ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:32:02.345 [XNIO-1 task-1] INFO  [ bcf69672f13ed80c , bcf69672f13ed80c ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:33:18.143 [XNIO-1 task-1] INFO  [ 33f0dc4fac834dae , 33f0dc4fac834dae ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-05 17:33:18.144 [XNIO-1 task-1] INFO  [ 33f0dc4fac834dae , 33f0dc4fac834dae ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:33:18.145 [XNIO-1 task-1] INFO  [ 33f0dc4fac834dae , 33f0dc4fac834dae ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:33:18.380 [XNIO-1 task-1] INFO  [ 33f0dc4fac834dae , 33f0dc4fac834dae ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-05 17:33:18.380 [XNIO-1 task-1] INFO  [ 33f0dc4fac834dae , 33f0dc4fac834dae ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@38cc3779], lock=Optional.empty, window=Optional.empty)
2025-08-05 17:33:18.380 [XNIO-1 task-1] INFO  [ 33f0dc4fac834dae , 33f0dc4fac834dae ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ?  ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59, 10]
2025-08-05 17:35:07.495 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-05 17:35:07.496 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:35:07.498 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:35:07.680 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
 LIMIT ? 
2025-08-05 17:35:07.681 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@23927d5d], lock=Optional.empty, window=Optional.empty)
2025-08-05 17:35:07.681 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
 LIMIT ?  ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59, 10]
2025-08-05 17:35:24.717 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-05 17:35:24.720 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:35:24.720 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:35:24.806 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
 LIMIT ? 
2025-08-05 17:35:24.807 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@23927d5d], lock=Optional.empty, window=Optional.empty)
2025-08-05 17:35:24.807 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
 LIMIT ?  ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59, 10]
2025-08-05 17:35:43.789 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-05 17:35:43.789 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:35:43.790 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:35:43.951 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-05 17:35:43.952 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@38cc3779], lock=Optional.empty, window=Optional.empty)
2025-08-05 17:35:43.952 [XNIO-1 task-1] INFO  [ 324832cc31afe895 , 324832cc31afe895 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ?  ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59, 10]
2025-08-05 17:41:55.219 [XNIO-1 task-1] INFO  [ 85fa8d28e74ec6ce , 85fa8d28e74ec6ce ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='null', createDateFrom=2025-07-05, createDateUntil=2025-08-05, cardId='null', customerId='null', cardStatus='null'}
2025-08-05 17:41:55.268 [XNIO-1 task-1] INFO  [ 85fa8d28e74ec6ce , 85fa8d28e74ec6ce ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (create_time >= ? AND create_time <= ?)
2025-08-05 17:41:55.268 [XNIO-1 task-1] INFO  [ 85fa8d28e74ec6ce , 85fa8d28e74ec6ce ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:41:55.268 [XNIO-1 task-1] INFO  [ 85fa8d28e74ec6ce , 85fa8d28e74ec6ce ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_1 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_2 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_3 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_5 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_6 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-07-05T00:00, 2025-08-05T23:59:59, 2025-07-05T00:00, 2025-08-05T23:59:59, 2025-07-05T00:00, 2025-08-05T23:59:59, 2025-07-05T00:00, 2025-08-05T23:59:59, 2025-07-05T00:00, 2025-08-05T23:59:59, 2025-07-05T00:00, 2025-08-05T23:59:59, 2025-07-05T00:00, 2025-08-05T23:59:59, 2025-07-05T00:00, 2025-08-05T23:59:59]
2025-08-05 17:43:00.757 [XNIO-1 task-1] INFO  [ 2a1f8836deae0086 , 2a1f8836deae0086 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='null', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='null', cardStatus='null'}
2025-08-05 17:43:00.775 [XNIO-1 task-1] INFO  [ 2a1f8836deae0086 , 2a1f8836deae0086 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (create_time >= ? AND create_time <= ?)
2025-08-05 17:43:00.775 [XNIO-1 task-1] INFO  [ 2a1f8836deae0086 , 2a1f8836deae0086 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:43:00.775 [XNIO-1 task-1] INFO  [ 2a1f8836deae0086 , 2a1f8836deae0086 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_1 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_2 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_3 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_5 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_6 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:43:08.612 [XNIO-1 task-1] INFO  [ 81b67acbd8176ae0 , 81b67acbd8176ae0 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='null', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='null', cardStatus='null'}
2025-08-05 17:43:08.625 [XNIO-1 task-1] INFO  [ 81b67acbd8176ae0 , 81b67acbd8176ae0 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (create_time >= ? AND create_time <= ?)
2025-08-05 17:43:08.626 [XNIO-1 task-1] INFO  [ 81b67acbd8176ae0 , 81b67acbd8176ae0 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:43:08.626 [XNIO-1 task-1] INFO  [ 81b67acbd8176ae0 , 81b67acbd8176ae0 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_1 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_2 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_3 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_5 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_6 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:44:52.615 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-05 17:49:04.821 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 17:49:04.889 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 17:49:05.540 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 17:49:05.540 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 17:49:07.282 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 17:49:07.333 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 17:49:08.703 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 17:49:08.708 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:49:08.759 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-05 17:49:09.009 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 17:49:09.321 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=7fb93919-1fb3-3bfd-a6a8-27ae86e3a07e
2025-08-05 17:49:09.446 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:49:09.447 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:49:09.447 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:49:09.448 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:49:09.450 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:49:09.455 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:49:10.055 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4b75073a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 17:49:10.905 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 17:49:10.905 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3553 ms
2025-08-05 17:49:22.213 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 17:49:29.046 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:29.140 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:29.156 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:29.998 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 17:49:29.999 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:49:29.999 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 17:49:36.464 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:36.497 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:37.103 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 17:49:38.275 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 17:49:40.616 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 17:49:47.587 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:47.988 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:36 - 初始化 AmazonS3 客户端，region: ap-east-1, bucket: qa-aws-static-s3
2025-08-05 17:49:47.988 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:51 - 使用自定义 S3 endpoint: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com
2025-08-05 17:49:47.992 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:64 - AmazonS3 客户端初始化完成
2025-08-05 17:49:48.296 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 17:49:48.341 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-05 17:49:48.744 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 17:49:50.373 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 17:49:50.422 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 17:49:50.423 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 17:49:50.433 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 17:49:50.435 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 17:49:50.435 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 17:49:50.435 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 17:49:50.436 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@79a8b897
2025-08-05 17:49:53.657 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-05 17:49:54.438 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a9d873b[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$86266986#mpcWalletEventRetryNotifyTask]
2025-08-05 17:49:54.439 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b2bd545[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$10744516#organizationFeeMonthlyReportTask]
2025-08-05 17:49:54.439 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@76600c90[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$d7715c08#organizationSMSFeeCalculateTask]
2025-08-05 17:49:54.440 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64f0271e[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$63c3f041#syncCustomerInfoTask]
2025-08-05 17:49:54.454 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:54.473 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 17:49:59.849 [Thread-138] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-05 17:50:06.139 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:50:06.140 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 17:50:17.334 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:50:17.335 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 17:50:28.541 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:50:28.542 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-05 17:50:39.737 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:50:39.738 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-05 17:50:47.909 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 17:50:47.909 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-05 17:50:48.043 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-05 17:50:48.078 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-05 17:50:48.172 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-05 17:50:48.256 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-05 17:50:48.346 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-05 17:50:48.385 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 17:50:48.385 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 17:50:48.964 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-05 17:50:48.968 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-05 17:50:48.969 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-05 17:50:49.007 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 104.526 seconds (JVM running for 110.836)
2025-08-05 17:50:49.038 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-05 17:50:49.038 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-05 17:50:49.038 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-05 17:50:49.272 [RMI TCP Connection(2)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 17:50:49.272 [RMI TCP Connection(2)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 17:50:49.279 [RMI TCP Connection(2)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 7 ms
2025-08-05 17:52:00.527 [XNIO-1 task-1] INFO  [ ca6fb3b4d2bbd4db , ca6fb3b4d2bbd4db ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='null', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='null', cardStatus='null'}
2025-08-05 17:52:01.193 [XNIO-1 task-1] INFO  [ ca6fb3b4d2bbd4db , ca6fb3b4d2bbd4db ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (create_time >= ? AND create_time <= ?)
2025-08-05 17:52:01.193 [XNIO-1 task-1] INFO  [ ca6fb3b4d2bbd4db , ca6fb3b4d2bbd4db ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:52:01.194 [XNIO-1 task-1] INFO  [ ca6fb3b4d2bbd4db , ca6fb3b4d2bbd4db ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_1 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_2 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_3 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_5 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_6 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:52:32.547 [XNIO-1 task-1] INFO  [ 46f86db7863c2a87 , 46f86db7863c2a87 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='null', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='null', cardStatus='null'}
2025-08-05 17:52:32.584 [XNIO-1 task-1] INFO  [ 46f86db7863c2a87 , 46f86db7863c2a87 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (create_time >= ? AND create_time <= ?)
2025-08-05 17:52:32.585 [XNIO-1 task-1] INFO  [ 46f86db7863c2a87 , 46f86db7863c2a87 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:52:32.585 [XNIO-1 task-1] INFO  [ 46f86db7863c2a87 , 46f86db7863c2a87 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_1 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_2 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_3 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_5 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_6 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-05 17:54:07.000 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='null', createDateFrom=2025-06-01, createDateUntil=2025-06-30, cardId='null', customerId='null', cardStatus='null'}
2025-08-05 17:54:07.017 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (create_time >= ? AND create_time <= ?)
2025-08-05 17:54:07.017 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:54:07.017 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_1 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_2 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_3 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_5 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_6 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 2025-06-01T00:00, 2025-06-30T23:59:59, 2025-06-01T00:00, 2025-06-30T23:59:59, 2025-06-01T00:00, 2025-06-30T23:59:59, 2025-06-01T00:00, 2025-06-30T23:59:59, 2025-06-01T00:00, 2025-06-30T23:59:59, 2025-06-01T00:00, 2025-06-30T23:59:59, 2025-06-01T00:00, 2025-06-30T23:59:59]
2025-08-05 17:54:07.248 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-05 17:54:07.248 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@7c024818], lock=Optional.empty, window=Optional.empty)
2025-08-05 17:54:07.248 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_0 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:07.248 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_1 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:07.248 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_2 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:07.249 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_3 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:07.249 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_4 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:07.249 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_5 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:07.249 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_6 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:07.249 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,card_scheme,card_product_code,card_id,card_no,masked_card_no,currency_code,processor,card_status,card_active_status,mobile_phone_area,mobile_phone,email,cardholder_first_name,cardholder_last_name,open_card_fee_detail_id,cancel_card_fee_detail_id,cancel_card_acceptance_fee_detail_id,create_time,last_modify_time  FROM kl_organization_customer_card_info_7 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-06-01T00:00, 2025-06-30T23:59:59, 10]
2025-08-05 17:54:08.932 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?))
2025-08-05 17:54:08.932 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 17:54:08.932 [XNIO-1 task-1] INFO  [ 271f1b51383c59b1 , 271f1b51383c59b1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_0 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) UNION ALL SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_1 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) UNION ALL SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_2 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) UNION ALL SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_3 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) UNION ALL SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_4 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) UNION ALL SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_5 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) UNION ALL SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_6 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) UNION ALL SELECT  account_no,business_system,account_type,currency_code,currency_precision,direction,status,minimum_amount,total_balance_amount,frozen_amount,available_amount,business_organization_no,business_customer_id,create_time,last_modify_time  FROM kl_account_info_7 
 
 WHERE (business_customer_id IN (?,?,?,?,?,?) AND currency_code IN (?,?)) ::: [UUID11, 23, 14, 18, 888888, 21, HKD, USD, UUID11, 23, 14, 18, 888888, 21, HKD, USD, UUID11, 23, 14, 18, 888888, 21, HKD, USD, UUID11, 23, 14, 18, 888888, 21, HKD, USD, UUID11, 23, 14, 18, 888888, 21, HKD, USD, UUID11, 23, 14, 18, 888888, 21, HKD, USD, UUID11, 23, 14, 18, 888888, 21, HKD, USD, UUID11, 23, 14, 18, 888888, 21, HKD, USD]
2025-08-05 17:58:34.202 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-05 18:10:02.109 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 18:10:02.172 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 18:10:02.781 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 18:10:02.781 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 18:10:04.501 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 18:10:04.538 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 18:10:05.689 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 18:10:05.693 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 18:10:05.735 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-05 18:10:05.956 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 18:10:06.270 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=7fb93919-1fb3-3bfd-a6a8-27ae86e3a07e
2025-08-05 18:10:06.389 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:10:06.390 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:10:06.391 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:10:06.392 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:10:06.394 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:10:06.399 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:10:06.995 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4b75073a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:10:07.834 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 18:10:07.834 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3280 ms
2025-08-05 18:10:19.386 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 18:10:26.321 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:26.423 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:26.440 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:27.277 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 18:10:27.277 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 18:10:27.278 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 18:10:33.735 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:33.768 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:34.375 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 18:10:35.644 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 18:10:37.916 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 18:10:44.979 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:45.379 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:36 - 初始化 AmazonS3 客户端，region: ap-east-1, bucket: qa-aws-static-s3
2025-08-05 18:10:45.380 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:51 - 使用自定义 S3 endpoint: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com
2025-08-05 18:10:45.384 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:64 - AmazonS3 客户端初始化完成
2025-08-05 18:10:45.699 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 18:10:45.742 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-05 18:10:46.151 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 18:10:47.797 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 18:10:47.845 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 18:10:47.845 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 18:10:47.855 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 18:10:47.857 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 18:10:47.857 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 18:10:47.857 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 18:10:47.858 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2532e07e
2025-08-05 18:10:51.080 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-05 18:10:51.858 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b2bd545[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$b8e1e1bc#mpcWalletEventRetryNotifyTask]
2025-08-05 18:10:51.859 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@76600c90[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$432fbd4c#organizationFeeMonthlyReportTask]
2025-08-05 18:10:51.859 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64f0271e[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$a2cd43e#organizationSMSFeeCalculateTask]
2025-08-05 18:10:51.859 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1222d0e4[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$967f6877#syncCustomerInfoTask]
2025-08-05 18:10:51.874 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:51.892 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:10:57.286 [Thread-138] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-05 18:11:03.597 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:11:03.599 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 18:11:14.814 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:11:14.816 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 18:11:26.032 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:11:26.033 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-05 18:11:37.197 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:11:37.198 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-05 18:11:45.558 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:11:45.560 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-05 18:11:45.713 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-05 18:11:45.748 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-05 18:11:45.765 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-05 18:11:45.851 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-05 18:11:45.947 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-05 18:11:45.986 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 18:11:45.986 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 18:11:46.652 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-05 18:11:46.656 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-05 18:11:46.656 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-05 18:11:46.705 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 104.906 seconds (JVM running for 111.131)
2025-08-05 18:11:46.739 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-05 18:11:46.740 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-05 18:11:46.740 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-05 18:11:46.856 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 18:11:46.857 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 18:11:46.863 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 6 ms
2025-08-05 18:11:55.226 [XNIO-1 task-1] INFO  [ 0202c0a8169d400a , 0202c0a8169d400a ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1084 ms
2025-08-05 18:12:27.374 [XNIO-1 task-1] INFO  [ e2c79356302e4df7 , e2c79356302e4df7 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=? 
2025-08-05 18:12:27.374 [XNIO-1 task-1] INFO  [ e2c79356302e4df7 , e2c79356302e4df7 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 18:12:27.374 [XNIO-1 task-1] INFO  [ e2c79356302e4df7 , e2c79356302e4df7 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=?  ::: [1952270967816888321]
2025-08-05 18:12:27.915 [XNIO-1 task-1] INFO  [ e2c79356302e4df7 , e2c79356302e4df7 ] c.k.l.c.service.export.ExportFileRecordService.generatePresignedUrl:186 - 生成临时下载链接成功，文件key: kl-static-file/authorization-export/Authorization_12001944_20250804153044.csv, 有效期至: Tue Aug 05 18:42:27 CST 2025
2025-08-05 18:12:27.915 [XNIO-1 task-1] INFO  [ e2c79356302e4df7 , e2c79356302e4df7 ] c.k.l.c.service.export.ExportFileRecordService.getUrlById:122 - 为文件记录ID: 1952270967816888321 生成临时下载链接成功
2025-08-05 18:20:12.188 [XNIO-1 task-1] INFO  [ d6adf6cbdf451215 , d6adf6cbdf451215 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=? 
2025-08-05 18:20:12.190 [XNIO-1 task-1] INFO  [ d6adf6cbdf451215 , d6adf6cbdf451215 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 18:20:12.190 [XNIO-1 task-1] INFO  [ d6adf6cbdf451215 , d6adf6cbdf451215 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=?  ::: [1952270967816888321]
2025-08-05 18:20:55.286 [XNIO-1 task-1] INFO  [ d6adf6cbdf451215 , d6adf6cbdf451215 ] c.k.l.c.service.export.ExportFileRecordService.generatePresignedUrl:186 - 生成临时下载链接成功，文件key: /kl-static-file/authorization-export/Authorization_12001944_20250804153044.csv, 有效期至: Tue Aug 05 18:50:55 CST 2025
2025-08-05 18:20:55.287 [XNIO-1 task-1] INFO  [ d6adf6cbdf451215 , d6adf6cbdf451215 ] c.k.l.c.service.export.ExportFileRecordService.getUrlById:122 - 为文件记录ID: 1952270967816888321 生成临时下载链接成功
2025-08-05 18:24:10.412 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-05 18:37:11.939 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 18:37:12.006 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 18:37:12.653 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 18:37:12.653 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 18:37:14.461 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 18:37:14.503 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 18:37:15.699 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 18:37:15.703 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 18:37:15.742 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-05 18:37:15.950 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 18:37:16.273 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=7fb93919-1fb3-3bfd-a6a8-27ae86e3a07e
2025-08-05 18:37:16.396 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:37:16.397 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:37:16.397 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:37:16.398 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:37:16.400 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:37:16.405 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:37:17.003 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4b75073a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:37:17.886 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 18:37:17.886 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3365 ms
2025-08-05 18:37:28.737 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 18:37:34.634 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:37:34.730 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:37:34.746 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:37:35.588 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 18:37:35.589 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 18:37:35.589 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 18:37:42.068 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:37:42.100 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:37:42.707 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 18:37:44.035 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 18:37:47.859 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 18:37:54.869 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:37:55.286 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:37 - 初始化 AmazonS3 客户端，region: ap-east-1, bucket: qa-aws-static-s3
2025-08-05 18:37:55.362 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:56 - 使用自定义 S3 endpoint: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com
2025-08-05 18:38:03.444 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-05 18:38:03.446 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-05 18:38:09.454 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-05 18:38:09.455 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-05 18:38:09.608 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-05 18:38:09.612 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-05 18:38:09.628 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-05 18:40:28.877 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 18:40:28.940 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 18:40:29.537 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 18:40:29.537 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 18:40:31.251 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-05 18:40:31.300 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 18:40:32.468 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 18:40:32.472 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 18:40:32.514 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-08-05 18:40:32.729 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 18:40:33.044 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=7fb93919-1fb3-3bfd-a6a8-27ae86e3a07e
2025-08-05 18:40:33.164 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:40:33.165 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:40:33.165 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:40:33.166 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:40:33.168 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:40:33.172 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:40:33.762 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4b75073a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 18:40:34.597 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 18:40:34.597 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3279 ms
2025-08-05 18:40:45.512 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 18:40:50.799 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:40:50.901 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:40:50.916 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:40:51.742 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 18:40:51.745 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 18:40:51.745 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 18:40:58.192 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:40:58.228 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:40:58.827 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 18:41:00.001 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 18:41:02.806 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 18:41:09.836 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:41:10.220 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:37 - 初始化 AmazonS3 客户端，region: ap-east-1, bucket: qa-aws-static-s3
2025-08-05 18:41:10.221 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:44 - 使用自定义 S3 endpoint: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com
2025-08-05 18:41:10.225 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AwsS3Config.amazonS3:67 - AmazonS3 客户端初始化完成
2025-08-05 18:41:10.543 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 18:41:10.586 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-05 18:41:11.015 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 18:41:12.633 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 18:41:12.680 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 18:41:12.681 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 18:41:12.691 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 18:41:12.693 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 18:41:12.693 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 18:41:12.693 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 18:41:12.693 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@18365e98
2025-08-05 18:41:15.872 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-05 18:41:16.673 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a584533[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$ff4493ce#mpcWalletEventRetryNotifyTask]
2025-08-05 18:41:16.674 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1cfb035c[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$89926f5e#organizationFeeMonthlyReportTask]
2025-08-05 18:41:16.674 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d22d4b9[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$508f8650#organizationSMSFeeCalculateTask]
2025-08-05 18:41:16.674 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@9b9a322[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$dce21a89#syncCustomerInfoTask]
2025-08-05 18:41:16.689 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:41:16.707 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 18:41:22.097 [Thread-134] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-05 18:41:28.401 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:41:28.403 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 18:41:39.613 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:41:39.615 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 18:41:50.821 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:41:50.822 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-05 18:42:02.031 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:42:02.032 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-05 18:42:10.347 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 18:42:10.347 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-05 18:42:10.475 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-05 18:42:10.518 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-05 18:42:10.535 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-05 18:42:10.633 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-05 18:42:10.729 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-05 18:42:10.769 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 18:42:10.770 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 18:42:11.444 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-05 18:42:11.448 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-05 18:42:11.448 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-05 18:42:11.493 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 102.929 seconds (JVM running for 109.079)
2025-08-05 18:42:11.528 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-05 18:42:11.528 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-05 18:42:11.528 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-05 18:42:11.958 [RMI TCP Connection(2)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 18:42:11.959 [RMI TCP Connection(2)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-05 18:42:11.967 [RMI TCP Connection(2)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-08-05 18:42:24.401 [XNIO-1 task-1] INFO  [ b73a4470f8024915 , b73a4470f8024915 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=? 
2025-08-05 18:42:24.402 [XNIO-1 task-1] INFO  [ b73a4470f8024915 , b73a4470f8024915 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 18:42:24.402 [XNIO-1 task-1] INFO  [ b73a4470f8024915 , b73a4470f8024915 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=?  ::: [1952270967816888321]
2025-08-05 18:42:24.933 [XNIO-1 task-1] INFO  [ b73a4470f8024915 , b73a4470f8024915 ] c.k.l.c.service.export.ExportFileRecordService.generatePresignedUrl:161 - 生成临时下载链接成功，文件key: kl-static-file/authorization-export/Authorization_12001944_20250804153044.csv, 有效期至: Tue Aug 05 19:12:24 CST 2025
2025-08-05 18:45:51.220 [XNIO-1 task-1] INFO  [ cc9d88be6aa8f58b , cc9d88be6aa8f58b ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=? 
2025-08-05 18:45:51.221 [XNIO-1 task-1] INFO  [ cc9d88be6aa8f58b , cc9d88be6aa8f58b ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 18:45:51.221 [XNIO-1 task-1] INFO  [ cc9d88be6aa8f58b , cc9d88be6aa8f58b ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT file_record_id,organization_no,file_name,file_type,file_size,s3_url,file_status,error_message,create_time,update_time FROM kl_export_file_record WHERE file_record_id=?  ::: [1952270967816888321]
2025-08-05 18:46:27.582 [XNIO-1 task-1] INFO  [ cc9d88be6aa8f58b , cc9d88be6aa8f58b ] c.k.l.c.service.export.ExportFileRecordService.generatePresignedUrl:161 - 生成临时下载链接成功，文件key: kl-static-file/authorization-export/Authorization_12001944_20250804153044.csv, 有效期至: Tue Aug 05 19:16:09 CST 2025
2025-08-05 18:51:36.197 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
