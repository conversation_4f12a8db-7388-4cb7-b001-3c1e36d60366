2025-08-05 08:52:52.689 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:52:52.696 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:52:53.697 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:52:58.004 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:53:54.197 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:53:54.200 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:54:12.873 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:54:12.878 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:54:13.879 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:54:17.217 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:54:23.245 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 08:54:23.248 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:11:35.642 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:11:35.647 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:57.450 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:57.453 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:59.844 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:59.847 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:59.849 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:59.851 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:59.855 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 09:28:59.861 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[] result: true
2025-08-05 15:21:29.903 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 15:21:29.976 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 15:21:30.674 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 15:21:30.674 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 15:21:32.460 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-05 15:21:32.500 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 15:21:34.244 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 15:21:34.248 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 15:21:34.280 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-05 15:21:34.475 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 15:21:34.825 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=ef027f8f-8ced-3571-9649-22570251af3c
2025-08-05 15:21:34.961 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:21:34.962 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:21:34.963 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/715950807] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:21:34.963 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:21:34.966 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:21:34.970 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:21:35.487 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$aaf55f0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:21:36.453 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 15:21:36.453 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3940 ms
2025-08-05 15:21:48.631 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 15:21:58.784 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-05 15:21:59.924 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 15:22:01.486 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 15:22:05.321 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 15:22:06.074 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:38
2025-08-05 15:22:07.090 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 15:22:07.090 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 15:22:07.090 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 15:22:14.441 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 15:22:14.992 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-05 15:22:15.261 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 15:22:15.447 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 15:22:15.473 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 15:22:17.125 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 15:22:18.579 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 15:22:18.626 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 15:22:18.626 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 15:22:18.636 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 15:22:18.638 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 15:22:18.638 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 15:22:18.638 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 15:22:18.638 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@53ef0db3
2025-08-05 15:22:22.973 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-05 15:22:32.704 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 15:22:32.705 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 15:22:39.074 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 15:22:39.076 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 15:22:50.282 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 15:22:50.282 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationTransAccountingReversalConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-05 15:22:50.422 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-05 15:22:50.458 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-05 15:22:50.474 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-05 15:22:50.555 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-05 15:22:50.653 [main] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-08-05 15:22:56.667 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 15:23:02.674 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 15:23:09.359 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 15:23:09.376 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-08-05 15:23:09.377 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-05 15:23:09.377 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-05 15:23:09.377 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-05 15:23:15.649 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-05 15:23:15.650 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-05 15:23:21.660 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-05 15:23:21.661 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-05 15:23:21.661 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-05 15:23:21.661 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-05 15:23:21.662 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-05 15:23:21.662 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-08-05 15:23:21.662 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-08-05 15:23:21.662 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-08-05 15:23:21.663 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-05 15:23:21.671 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-05 15:23:21.690 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-05 16:00:32.173 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-05 16:58:20.894 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-05 16:58:20.961 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-05 16:58:21.650 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-05 16:58:21.650 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-05 16:58:23.455 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-05 16:58:23.487 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-05 16:58:24.720 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 16:58:24.724 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 16:58:24.756 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-05 16:58:24.961 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-05 16:58:25.292 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=ef027f8f-8ced-3571-9649-22570251af3c
2025-08-05 16:58:25.424 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:58:25.425 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:58:25.425 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/715950807] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:58:25.426 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:58:25.429 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:58:25.434 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:58:25.942 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$aaf55f0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:58:26.892 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-05 16:58:26.893 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3392 ms
2025-08-05 16:58:37.625 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-05 16:58:47.461 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-05 16:58:48.609 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-05 16:58:49.864 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 16:58:54.987 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-05 16:58:55.759 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:39
2025-08-05 16:58:56.798 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-05 16:58:56.799 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-05 16:58:56.799 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-05 16:59:03.778 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 16:59:04.306 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-05 16:59:04.408 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-05 16:59:04.509 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 16:59:04.529 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-05 16:59:05.843 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-05 16:59:07.230 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-05 16:59:07.278 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-05 16:59:07.278 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-05 16:59:07.288 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-05 16:59:07.291 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-05 16:59:07.291 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-05 16:59:07.291 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-05 16:59:07.291 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@571f6b61
2025-08-05 16:59:10.667 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-05 16:59:20.317 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 16:59:20.318 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-05 16:59:26.753 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-05 16:59:26.754 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-05 16:59:37.987 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
