ALTER TABLE `up_app_user_info`
ADD COLUMN `kyc_apply_country_code` varchar(4) NULL COMMENT 'kyc申请是选的国家代码，3位字母' AFTER `country_no`,
ADD COLUMN `card_count` int DEFAULT 0 COMMENT '卡片激活数' AFTER `risk_status`,
ADD COLUMN `card_product_id` varchar(32) NULL COMMENT '卡产品id' AFTER `card_count`;


ALTER TABLE `up_app_user_employment_info`
ADD COLUMN `risk_case_no` varchar(64)  DEFAULT NULL COMMENT '风控返回案件号' AFTER `app_user_login_id`,
ADD COLUMN `risk_status` varchar(2)  DEFAULT '0' COMMENT '0:风控审核中；1：风控审核通过；2：风控审核拒绝' AFTER `risk_case_no`,
ADD COLUMN `risk_reject_reason` varchar(255)  DEFAULT NULL COMMENT '风控拒绝原因' AFTER `risk_status`;


ALTER TABLE `up_kyc_level1_info`
ADD COLUMN `kyc_apply_country_code` varchar(4) DEFAULT NULL COMMENT 'kyc申请是选的国家代码，3位字母' AFTER `update_datetime`;


CREATE TABLE `up_app_kyc_apply` (
                                    `kyc_apply_id` varchar(32) NOT NULL COMMENT 'kyc申请主键id',
                                    `app_user_login_id` bigint DEFAULT NULL COMMENT 'app登录用户id',
                                    `organization_no` varchar(32)  DEFAULT NULL COMMENT '来源；UCard:ECT2025052799991;',
                                    `kyc_apply_country_code` varchar(4)  DEFAULT NULL COMMENT 'kyc申请的国家码',
                                    `transaction_id` varchar(64) DEFAULT NULL COMMENT 'zoloz 生成的唯一事务id',
                                    `last_name` varchar(64)  DEFAULT NULL COMMENT '姓',
                                    `middle_name` varchar(64)  DEFAULT NULL COMMENT '中间名',
                                    `first_name` varchar(64)  DEFAULT NULL COMMENT '名',
                                    `id_type` varchar(16)  DEFAULT NULL COMMENT '证件类型',
                                    `id_no` varchar(255)  DEFAULT NULL COMMENT '加密,证件号',
                                    `masked_id_no` varchar(64)  DEFAULT NULL COMMENT '脱敏的证件号',
                                    `birth_date` varchar(10)  DEFAULT NULL COMMENT '出生日期;yyyy/mm/dd',
                                    `gender` int DEFAULT NULL COMMENT '性别;1:男;2:女',
                                    `id_issue_date` varchar(10)  DEFAULT NULL COMMENT '证件签发日',
                                    `id_expiry_date` varchar(10)  DEFAULT NULL COMMENT '证件有效期 ',
                                    `nationality` varchar(32)  DEFAULT NULL COMMENT '国籍',
                                    `country_code` varchar(3)  DEFAULT NULL COMMENT '国家代码,3位字母',
                                    `country_no` varchar(3)  DEFAULT NULL COMMENT '国家地区代码,3位数字',
                                    `id_card_front_image` varchar(256)  DEFAULT NULL COMMENT '证件正面地址',
                                    `face_photo_image` varchar(256)  DEFAULT NULL COMMENT '人脸图片地址',
                                    `face_score` int DEFAULT NULL COMMENT '活体分',
                                    `apply_status` varchar(2)  DEFAULT '0' COMMENT '认证状态;0:认证中;1：认证成功;2:认证失败;',
                                    `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
                                    `card_product_id` varchar(32) DEFAULT NULL COMMENT '申请卡产品id',
                                    `certification_start_time` datetime DEFAULT NULL COMMENT '认证开始时间',
                                    `certification_completion_time` datetime DEFAULT NULL COMMENT '认证完成时间',
                                    PRIMARY KEY (`kyc_apply_id`),
                                    KEY `idx_login_id` (`app_user_login_id`)
)  COMMENT='app用户kyc申请';




UPDATE up_country_region
SET is_valid = '0'
WHERE country_code_3_numeric IN(
                                004,
                                008,
                                016,
                                070,
                                854,
                                108,
                                112,
                                180,
                                140,
                                178,
                                192,
                                232,
                                230,
                                624,
                                334,
                                191,
                                332,
                                368,
                                364,
                                408,
                                422,
                                430,
                                434,
                                499,
                                807,
                                466,
                                104,
                                558,
                                275,
                                900,
                                688,
                                643,
                                705,
                                706,
                                728,
                                760,
                                260,
                                804,
                                581,
                                840,
                                862,
                                850,
                                886,
                                716
    );