ALTER TABLE `kc_clearing_info_2025_q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2025_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2025_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2025_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2026_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2026_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2026_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2026_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2027_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2027_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2027_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2027_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2028_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2028_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2028_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2028_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2029_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2029_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2029_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2029_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2030_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2030_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2030_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2030_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2031_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2031_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2031_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2031_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2032_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2032_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2032_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2032_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2033_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2033_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2033_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2033_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2034_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2034_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2034_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2034_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2035_Q1`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2035_Q2`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2035_Q3`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';

ALTER TABLE `kc_clearing_info_2035_Q4`
ADD COLUMN  `cardholder_billing_rate` decimal(10,5)  DEFAULT 1.00 COMMENT '持卡人利率',
ADD COLUMN  `cardholder_billing_rate_source` varchar(16)  DEFAULT 'DEFAULT' COMMENT '持卡人利率来源;DEFAULT,PAY_X,AUTH_FLOW',
ADD COLUMN  `destination_currency_code` varchar(4)  DEFAULT NULL COMMENT '清分文件中的目标币种,3位字母',
ADD COLUMN  `destination_currency_amount` decimal(18,3)  DEFAULT NULL COMMENT '清分文件中的目标金额';