package com.kun.linkage.common.external.facade.api.kcard.enums;

public enum KunAndPayXDebitSubRefundStatusEnum {
    NOT_FINISHED_PROHIBIT("NOT_FINISHED_PROHIBIT", "未结束禁止撤销"),
    PROCESS("PROCESS", "撤销中"),
    SUCCESS("SUCCESS", "撤销完成"),
    FAIL_PROHIBIT("FAIL_PROHIBIT", "失败禁止撤销");

    private final String status;
    private final String desc;

    KunAndPayXDebitSubRefundStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
