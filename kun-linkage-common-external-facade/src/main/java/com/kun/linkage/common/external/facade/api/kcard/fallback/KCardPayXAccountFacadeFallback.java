package com.kun.linkage.common.external.facade.api.kcard.fallback;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.utils.FallbackExceptionUtil;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubRefundReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXQueryBalanceReq;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXQueryBalanceRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/4 11:37
 */
@Component
@Slf4j
public class KCardPayXAccountFacadeFallback implements FallbackFactory<KCardPayXAccountFacade> {
    @Override
    public KCardPayXAccountFacade create(Throwable cause) {
        log.info("Creating fallback for {}, cause: {}", KCardPayXAccountFacade.class.getName(), cause.getMessage());
        return new KCardPayXAccountFacade() {

            /**
             * PayX-集团内询价接口
             *
             * @param req
             * @return
             */
            @Override
            public Result<PayXAskPriceRsp> payXExchangeRate(PayXAskPriceReq req) {
                log.error("{}.{} invoke is failed", KCardPayXAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }

            /**
             * PayX-集团内部产品扣账接口
             *
             * @param req
             * @return
             */
            @Override
            public Result<PayXDebitSubRsp> payXDebitSub(PayXDebitSubReq req) {
                log.error("{}.{} invoke is failed", KCardPayXAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }

            /**
             * PayX-撤销扣款接口-幂等
             *
             * @param req
             * @return
             */
            @Override
            public Result<PayXDebitSubRefundRsp> payXDebitSubRefund(PayXDebitSubRefundReq req) {
                log.error("{}.{} invoke is failed", KCardPayXAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }

            /**
             * title: <br>
             *
             * @param payXQueryBalanceReq
             * @description: PayX-查询子账户信息
             * Copyright: Copyright (c)2014<br>
             * Company: 易宝支付(YeePay)<br>
             * <AUTHOR>
             * @version 1.0.0
             * @since 2025/7/4 11:07
             */
            @Override
            public Result<PayXQueryBalanceRsp> payxQueryBalnace(PayXQueryBalanceReq payXQueryBalanceReq) {
                log.error("{}.{} invoke is failed", KCardPayXAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }
        };
    }
}
