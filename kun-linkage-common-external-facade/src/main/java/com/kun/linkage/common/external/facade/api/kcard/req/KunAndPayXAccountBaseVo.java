package com.kun.linkage.common.external.facade.api.kcard.req;

import java.io.Serializable;

public class KunAndPayXAccountBaseVo implements Serializable {
    /**
     * 令牌
     */
    private String token;
    /**
     * 产品代码
     */
    private String groupProductCode;
    /**
     * 交易流水号，通常为时间戳，最大长度32位
     */
    private String transSeqNo;
    /**
     * 账户号
     */
    private String accountNo;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getGroupProductCode() {
        return groupProductCode;
    }

    public void setGroupProductCode(String groupProductCode) {
        this.groupProductCode = groupProductCode;
    }

    public String getTransSeqNo() {
        return transSeqNo;
    }

    public void setTransSeqNo(String transSeqNo) {
        this.transSeqNo = transSeqNo;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
}
