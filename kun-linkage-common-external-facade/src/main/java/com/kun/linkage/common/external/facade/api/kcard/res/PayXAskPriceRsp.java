package com.kun.linkage.common.external.facade.api.kcard.res;

import java.math.BigDecimal;

public class PayXAskPriceRsp {
    /**
     * 汇率
     */
    private BigDecimal exchangeRate;
    /**
     * 卖出币种
     */
    private String sellCcy;
    /**
     * 买入币种
     */
    private String buyCcy;

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getSellCcy() {
        return sellCcy;
    }

    public void setSellCcy(String sellCcy) {
        this.sellCcy = sellCcy;
    }

    public String getBuyCcy() {
        return buyCcy;
    }

    public void setBuyCcy(String buyCcy) {
        this.buyCcy = buyCcy;
    }
}
