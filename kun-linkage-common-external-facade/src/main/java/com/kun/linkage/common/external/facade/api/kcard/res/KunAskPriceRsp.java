package com.kun.linkage.common.external.facade.api.kcard.res;

import java.math.BigDecimal;

public class KunAskPriceRsp {
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 令牌
     */
    private String token;

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
