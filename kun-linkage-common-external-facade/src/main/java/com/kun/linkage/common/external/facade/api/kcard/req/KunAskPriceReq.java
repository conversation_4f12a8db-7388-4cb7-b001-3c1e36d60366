package com.kun.linkage.common.external.facade.api.kcard.req;

import java.math.BigDecimal;

public class KunAskPriceReq extends KunAndPayXAccountBaseVo {
    /**
     * 金额
     */
    private BigDecimal payAmount;
    /**
     * 方向（BUY：法转数，SELL：数转法）
     */
    private String sideType;
    /**
     * 币对（USDT_HKD）
     */
    private String symbol;

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getSideType() {
        return sideType;
    }

    public void setSideType(String sideType) {
        this.sideType = sideType;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
}
