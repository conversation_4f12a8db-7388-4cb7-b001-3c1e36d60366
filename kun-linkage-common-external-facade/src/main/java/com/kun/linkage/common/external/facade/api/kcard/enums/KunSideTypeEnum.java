package com.kun.linkage.common.external.facade.api.kcard.enums;

public enum KunSideTypeEnum {
    BUY("BUY", "法币转数币"),
    SELL("SELL", "数币转法币");

    private final String type;
    private final String desc;

    KunSideTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
