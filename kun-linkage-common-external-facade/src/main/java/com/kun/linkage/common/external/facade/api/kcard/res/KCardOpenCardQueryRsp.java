package com.kun.linkage.common.external.facade.api.kcard.res;

import java.io.Serializable;

/**
 * 开卡结果查询-响应对象
 */
public class KCardOpenCardQueryRsp extends KCardResultBaseVo implements Serializable {
    /**
     * 开卡结果
     */
    private String openCardStatus;

    /**
     * 卡号
     */
    private String cardNo;

    public String getOpenCardStatus() {
        return openCardStatus;
    }

    public void setOpenCardStatus(String openCardStatus) {
        this.openCardStatus = openCardStatus;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }
}
