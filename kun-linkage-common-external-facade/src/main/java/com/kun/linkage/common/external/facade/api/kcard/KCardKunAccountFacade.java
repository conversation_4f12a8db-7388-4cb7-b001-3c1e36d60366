package com.kun.linkage.common.external.facade.api.kcard;


import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubRefundReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunQueryBalanceReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunQueryBalanceRsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * KCard转发KUN账户接口
 */
@FeignClient(name = "kcard-gateway", path = "/gateway/account/transaction/kun")
public interface KCardKunAccountFacade {
    /**
     * 获取KUN汇率
     * @param req
     * @return
     */
    @RequestMapping("/kunExchangeRate")
    Result<KunAskPriceRsp> kunExchangeRate(@RequestBody KunAskPriceReq req);
    /**
     * KUN-集团内部产品扣账接口
     * @param req
     * @return
     */
    @RequestMapping("/kunDebitSub")
    Result<KunDebitSubRsp> kunDebitSub(@RequestBody KunDebitSubReq req);
    /**
     * KUN-集团内部产品撤销扣账接口-幂等
     * @param req
     * @return
     */
    @RequestMapping("/kunDebitSubRefund")
    Result<KunDebitSubRefundRsp> kunDebitSubRefund(@RequestBody KunDebitSubRefundReq req);

    /**
     * title: <br>
     * @description: KUN-查询子账户信息
     * Copyright: Copyright (c)2014<br>
     * Company: 易宝支付(YeePay)<br>
     *
     * <AUTHOR>
     * @version 1.0.0
     * @since 2025/7/4 11:05
     */
    @RequestMapping(value = "/kunQueryBalnace", method = RequestMethod.POST)
    Result<KunQueryBalanceRsp> kunQueryBalnace(@RequestBody KunQueryBalanceReq kunQueryBalanceReq);
}
