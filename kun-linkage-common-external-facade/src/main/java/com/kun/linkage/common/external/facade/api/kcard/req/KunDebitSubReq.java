package com.kun.linkage.common.external.facade.api.kcard.req;

import java.math.BigDecimal;

public class KunDebitSubReq extends KunAndPayXAccountBaseVo {
    /**
     * 交易请求号
     */
    private String requestNo;
    /**
     * 交易币种（USDT）
     */
    private String currency;
    /**
     * 交易金额
     */
    private BigDecimal amount;
    /**
     * 扣款方向（TO_USER 从集团账户扣到客户，TO_GROUP 从客户扣到集团账户）
     */
    private String direction;
    /**
     * 备注
     */
    private String remark;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
