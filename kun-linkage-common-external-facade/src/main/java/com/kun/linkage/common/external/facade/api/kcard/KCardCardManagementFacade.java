package com.kun.linkage.common.external.facade.api.kcard;


import com.kun.linkage.common.external.facade.api.kcard.req.*;
import com.kun.linkage.common.external.facade.api.kcard.res.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * kcard卡管理接口
 */
@FeignClient(name = "kcard-gateway", path = "/gateway/card/v2")
public interface KCardCardManagementFacade {
    /**
     * 开卡接口
     *
     * @param kCardOpenCardReq
     */
    @RequestMapping(value = "/openCard", method = RequestMethod.POST)
    KCardOpenCardRsp openCard(@RequestBody KCardOpenCardReq kCardOpenCardReq);

    /**
     * 开卡结果查询
     *
     * @param kCardOpenCardQueryReq
     */
    @RequestMapping(value = "/openCardStatusQuery", method = RequestMethod.POST)
    KCardOpenCardQueryRsp openCardStatusQuery(@RequestBody KCardOpenCardQueryReq kCardOpenCardQueryReq);

    /**
     * 卡冻结/解冻
     *
     * @param kCardCardFreezeReq
     */
    @RequestMapping(value = "/cardFreeze", method = RequestMethod.POST)
    KCardCardFreezeRsp cardFreeze(@RequestBody KCardCardFreezeReq kCardCardFreezeReq);

    /**
     * 销卡
     *
     * @param kCardCancelCardReq
     */
    @RequestMapping(value = "/cancelCard", method = RequestMethod.POST)
    KCardCancelCardRsp cancelCard(@RequestBody KCardCancelCardReq kCardCancelCardReq);

    /**
     * 卡信息查询
     *
     * @param kCardCardInfoQueryReq
     */
    @RequestMapping(value = "/cardInfoQuery", method = RequestMethod.POST)
    KCardCardInfoQueryRsp cardInfoQuery(@RequestBody KCardCardInfoQueryReq kCardCardInfoQueryReq);

    /**
     * 卡激活
     *
     * @param kCardCardActivationReq
     */
    @RequestMapping(value = "/cardActivation", method = RequestMethod.POST)
    KCardCardActivationRsp cardActivation(@RequestBody KCardCardActivationReq kCardCardActivationReq);
}
