package com.kun.linkage.common.external.facade.api.kcard.constants;


/**
 * 错误码枚举
 */
public enum KCardApiErrorCode {


    SUCCESS("000000", "请求成功"),
    FAIL("999999", "系统异常"),
    BUSINESS_FAIL("999998", "业务系统异常"),
    PARAM_ERROR("999997", "参数错误"),

    OPEN_CARD_FAIL("121101", "开卡失败"),
    THE_AMOUNT_TO_BE_OPENED_CANNOT_BE_LESS_THAN_0("121102", "借记卡类型开卡金额不能小于0"),
    META_IS_NULL("121103", "开卡参数Meta为空"),
    THE_CARD_TRANSACTION_TIMES_CANNOT_BE_LESS_THAN_0("121104", "刷卡次数不能少于0"),

    CHANNEL_CALL_EXCEPTION("121201", "渠道调用异常!"),
    CARD_NOT_FOUND("121202", "卡片未查询到!"),


    RECHARGE_AMOUNT_ERROR("121210", "充值金额错误!"),
    RECHARGE_AMOUNT_200_ERROR("121211", "充值金额需大于200元!"),
    RECHARGE_AMOUNT_FORMAT_ERROR("121212", "充值金额格式错误!"),
    ;


    private String code;
    private String message;

    KCardApiErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }


}
