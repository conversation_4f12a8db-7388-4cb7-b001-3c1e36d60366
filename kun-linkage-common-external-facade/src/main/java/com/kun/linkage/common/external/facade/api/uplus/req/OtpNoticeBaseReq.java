package com.kun.linkage.common.external.facade.api.uplus.req;

import java.io.Serializable;

public class OtpNoticeBaseReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求流水号
     */
    private String requestId;

    /**
     * kCard 生成的卡id
     */
    private String kCardId;

    /**
     * 卡片后四位
     */
    private String cardLastFour;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码/OTP
     */
    private String pwd;

    /**
     * 金额
     */
    private String amount;

    /**
     * 货币类型
     */
    private String crnc;

    /**
     * 商户名称
     */
    private String mrchtName;

    public OtpNoticeBaseReq() {
    }

    public OtpNoticeBaseReq(String requestId, String kCardId, String cardLastFour, String phone, String email, String pwd, String amount, String crnc, String mrchtName) {
        this.requestId = requestId;
        this.kCardId = kCardId;
        this.cardLastFour = cardLastFour;
        this.phone = phone;
        this.email = email;
        this.pwd = pwd;
        this.amount = amount;
        this.crnc = crnc;
        this.mrchtName = mrchtName;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getkCardId() {
        return kCardId;
    }

    public void setkCardId(String kCardId) {
        this.kCardId = kCardId;
    }

    public String getCardLastFour() {
        return cardLastFour;
    }

    public void setCardLastFour(String cardLastFour) {
        this.cardLastFour = cardLastFour;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCrnc() {
        return crnc;
    }

    public void setCrnc(String crnc) {
        this.crnc = crnc;
    }

    public String getMrchtName() {
        return mrchtName;
    }

    public void setMrchtName(String mrchtName) {
        this.mrchtName = mrchtName;
    }
}
