package com.kun.linkage.common.external.facade.api.kcard.fallback;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.utils.FallbackExceptionUtil;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubRefundReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunQueryBalanceReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunQueryBalanceRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/4 11:33
 */
@Slf4j
@Component
public class KCardKunAccountFacadeFallback implements FallbackFactory<KCardKunAccountFacade> {

    @Override
    public KCardKunAccountFacade create(Throwable cause) {
        log.info("Creating fallback for {}, cause: {}", KCardKunAccountFacade.class.getName(), cause.getMessage());
        return new KCardKunAccountFacade() {

            /**
             * 获取KUN汇率
             *
             * @param req
             * @return
             */
            @Override
            public Result<KunAskPriceRsp> kunExchangeRate(KunAskPriceReq req) {
                log.error("{}.{} invoke is failed", KCardKunAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }

            /**
             * KUN-集团内部产品扣账接口
             *
             * @param req
             * @return
             */
            @Override
            public Result<KunDebitSubRsp> kunDebitSub(KunDebitSubReq req) {
                log.error("{}.{} invoke is failed", KCardKunAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }

            /**
             * KUN-集团内部产品撤销扣账接口-幂等
             *
             * @param req
             * @return
             */
            @Override
            public Result<KunDebitSubRefundRsp> kunDebitSubRefund(KunDebitSubRefundReq req) {
                log.error("{}.{} invoke is failed", KCardKunAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }

            /**
             * title: <br>
             *
             * @param kunQueryBalanceReq
             * @description: KUN-查询子账户信息
             * Copyright: Copyright (c)2014<br>
             * Company: 易宝支付(YeePay)<br>
             * <AUTHOR>
             * @version 1.0.0
             * @since 2025/7/4 11:05
             */
            @Override
            public Result<KunQueryBalanceRsp> kunQueryBalnace(KunQueryBalanceReq kunQueryBalanceReq) {
                log.error("{}.{} invoke is failed", KCardKunAccountFacade.class.getName(),
                        Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return FallbackExceptionUtil.getFallbackResult(cause);
            }
        };
    }
}
