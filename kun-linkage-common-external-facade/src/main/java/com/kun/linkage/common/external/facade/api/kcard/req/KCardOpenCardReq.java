package com.kun.linkage.common.external.facade.api.kcard.req;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 开卡接口-请求对象
 */
public class KCardOpenCardReq extends KCardRequestBaseVo implements Serializable {
    /**
     * 外部用户ID
     */
    private String outUserId;
    /**
     * 外部用户名称
     */
    private String outUserName;
    /**
     * 开卡模式(开卡模式:开卡模式:1:多次卡;2:单次卡)
     */
    private String cardMode;
    /**
     * 卡片类型(卡类型:1:非额度卡;2:额度卡;3:账户卡;)
     */
    private String cardType;
    /**
     * 卡片币种
     * HKD: 港币
     */
    private String openCurrency;
    /**
     * 开卡金额
     */
    private BigDecimal openAmount;
    /**
     * 卡产品编号
     */
    private String cardProductCode;
    /**
     * 处理方
     */
    private String processor;
    /**
     * 卡片币种
     */
    private String cardScheme;
    /**
     * IP
     */
    private String ip;
    /**
     * 额外信息
     */
    private Meta meta;
    /**
     * "卡片有效期
     * 格式：yyyy-mm-dd
     * 最少1m 最多3y"
     */
    private Date expiryDate;
    /**
     * 卡片BIN
     */
    private String cardBin;

    public static class Meta {
        /**
         * 手机区号
         */
        private String dialCode;
        /**
         * 手机号
         */
        private String phone;
        /**
         * 邮箱
         */
        private String email;

        /**
         * 卡片名称
         */
        private String cardName;
        /**
         * 持卡人数字国家码
         */
        private String cardHolderCountryNo;
        /**
         * 持卡人姓
         */
        private String cardHolderFirstName;
        /**
         * 持卡人名
         */
        private String cardHolderLastName;

        public String getDialCode() {
            return dialCode;
        }

        public void setDialCode(String dialCode) {
            this.dialCode = dialCode;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getCardName() {
            return cardName;
        }

        public void setCardName(String cardName) {
            this.cardName = cardName;
        }

        public String getCardHolderFirstName() {
            return cardHolderFirstName;
        }

        public void setCardHolderFirstName(String cardHolderFirstName) {
            this.cardHolderFirstName = cardHolderFirstName;
        }

        public String getCardHolderLastName() {
            return cardHolderLastName;
        }

        public void setCardHolderLastName(String cardHolderLastName) {
            this.cardHolderLastName = cardHolderLastName;
        }

        public String getCardHolderCountryNo() {
            return cardHolderCountryNo;
        }

        public void setCardHolderCountryNo(String cardHolderCountryNo) {
            this.cardHolderCountryNo = cardHolderCountryNo;
        }
    }

    public String getOutUserId() {
        return outUserId;
    }

    public void setOutUserId(String outUserId) {
        this.outUserId = outUserId;
    }

    public String getOutUserName() {
        return outUserName;
    }

    public void setOutUserName(String outUserName) {
        this.outUserName = outUserName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public String getCardScheme() {
        return cardScheme;
    }

    public void setCardScheme(String cardScheme) {
        this.cardScheme = cardScheme;
    }

    public String getOpenCurrency() {
        return openCurrency;
    }

    public void setOpenCurrency(String openCurrency) {
        this.openCurrency = openCurrency;
    }

    public BigDecimal getOpenAmount() {
        return openAmount;
    }

    public void setOpenAmount(BigDecimal openAmount) {
        this.openAmount = openAmount;
    }

    @Override
    public String getIp() {
        return ip;
    }

    @Override
    public void setIp(String ip) {
        this.ip = ip;
    }

    public Meta getMeta() {
        return meta;
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getCardBin() {
        return cardBin;
    }

    public void setCardBin(String cardBin) {
        this.cardBin = cardBin;
    }

    public String getCardMode() {
        return cardMode;
    }

    public void setCardMode(String cardMode) {
        this.cardMode = cardMode;
    }
}
