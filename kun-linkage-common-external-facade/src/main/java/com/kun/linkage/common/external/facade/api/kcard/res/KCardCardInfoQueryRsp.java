package com.kun.linkage.common.external.facade.api.kcard.res;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 卡信息查询-响应对象
 */
public class KCardCardInfoQueryRsp extends KCardResultBaseVo implements Serializable {
    /**
     * 卡ID
     */
    private String cardId;
    /**
     * 加密卡号
     */
    private String cardNo;
    /**
     * 掩码卡号
     */
    private String cardNoMask;
    /**
     * 卡类型:1:额度卡;2:非额度卡;3:账户卡
     */
    private String cardType;
    /**
     * 加密CVV
     */
    private String cvv;
    /**
     * 有效期
     * 格式：yyyy-mm-dd
     */
    private String expiryDate;
    /**
     * 余额
     */
    private BigDecimal balance;
    /**
     * 卡片状态
     */
    private String status;
    /**
     * 卡片激活状态
     */
    private String cardActiveStatus;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardNoMask() {
        return cardNoMask;
    }

    public void setCardNoMask(String cardNoMask) {
        this.cardNoMask = cardNoMask;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCardActiveStatus() {
        return cardActiveStatus;
    }

    public void setCardActiveStatus(String cardActiveStatus) {
        this.cardActiveStatus = cardActiveStatus;
    }
}
