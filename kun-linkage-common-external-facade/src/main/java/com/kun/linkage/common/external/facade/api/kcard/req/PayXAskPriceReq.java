package com.kun.linkage.common.external.facade.api.kcard.req;

public class PayXAskPriceReq extends KunAndPayXAccountBaseVo {
    /**
     * 卖出币种（AUD,CAD,EUR,JPY,NZD,NOK,GBP,SEK,CHF,USD,SGD,HKD,CNH,PHP,VND）
     */
    private String sourceCurrency;
    /**
     * 买入币种（AUD,CAD,EUR,JPY,NZD,NOK,GBP,SEK,CHF,USD,SGD,HKD,CNH,PHP,VND）
     */
    private String targetCurrency;

    public String getTargetCurrency() {
        return targetCurrency;
    }

    public void setTargetCurrency(String targetCurrency) {
        this.targetCurrency = targetCurrency;
    }

    public String getSourceCurrency() {
        return sourceCurrency;
    }

    public void setSourceCurrency(String sourceCurrency) {
        this.sourceCurrency = sourceCurrency;
    }
}
