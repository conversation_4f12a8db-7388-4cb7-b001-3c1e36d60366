package com.kun.linkage.common.external.facade.api.kcard.req;
import java.io.Serializable;

public class KCardRequestBaseVo implements Serializable {
    /**
     * 来源系统;VCC;KL;
     * 当前系统为KL直接写死
     */
    private String system = "KL";
    /**
     * 商户编号
     */
    private String customerId;
    /**
     * 请求号
     */
    private String requestNo;
    /**
     * ip
     */
    private String ip;

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
