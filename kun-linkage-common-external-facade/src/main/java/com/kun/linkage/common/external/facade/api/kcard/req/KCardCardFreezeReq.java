package com.kun.linkage.common.external.facade.api.kcard.req;

public class KCardCardFreezeReq extends KCardRequestBaseVo {

    private String cardId;
    /**
     * 操作类型
     * FREEZE - 冻结
     * THAWED - 解冻
     */
    private String operationType;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }
}