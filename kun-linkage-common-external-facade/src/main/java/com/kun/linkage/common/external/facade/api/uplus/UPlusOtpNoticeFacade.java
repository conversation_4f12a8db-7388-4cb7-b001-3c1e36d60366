package com.kun.linkage.common.external.facade.api.uplus;


import com.kun.linkage.common.base.Result;

import com.kun.linkage.common.external.facade.api.uplus.req.OtpNoticeBaseReq;
import com.kun.linkage.common.external.facade.api.uplus.res.OtpNoticeBaseRes;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * UP otp 通知
 */
@FeignClient(name = "uplus-user", path = "/uplus-user/api/notify")
public interface UPlusOtpNoticeFacade {


    /**
     * otp 通知转发
     * @param otpNoticeBaseReq otp 通知转发
     * @return
     */
    @Operation(summary = "otp转发")
    @PostMapping("/otp")
    public Result<OtpNoticeBaseRes> optNotice(@RequestBody OtpNoticeBaseReq otpNoticeBaseReq);

}
