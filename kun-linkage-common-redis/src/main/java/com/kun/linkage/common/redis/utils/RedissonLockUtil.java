package com.kun.linkage.common.redis.utils;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RedissonLockUtil {
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private Environment environment;

    /**
     * 获取非公平锁
     *
     * @param key 锁key
     * @return 锁对象
     */
    public RLock getLock(String key) {
        return redissonClient.getLock(this.formatKey(key));
    }

    /**
     * 获取公平锁
     *
     * @param key 锁key
     * @return 锁对象
     */
    public RLock getFairLock(String key) {
        return redissonClient.getFairLock(this.formatKey(key));
    }

    /**
     * 释放锁
     *
     * @param lock 锁对象
     */
    public void unlock(RLock lock) {
        if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    /**
     * 格式化key(服务名+前缀+key)
     * @param key
     * @return
     */
    private String formatKey(String key) {
        return environment.getProperty("spring.application.name") + ":lock_key:" + key;
    }

}
