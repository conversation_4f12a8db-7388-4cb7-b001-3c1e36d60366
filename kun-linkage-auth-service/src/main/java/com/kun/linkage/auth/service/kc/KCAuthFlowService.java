package com.kun.linkage.auth.service.kc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailRequestVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowPageVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowRequestVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.KCAuthFlow;
import com.kun.linkage.common.db.entity.KCKycMainInfo;
import com.kun.linkage.common.db.mapper.KCAuthFlowMapper;
import com.kun.linkage.common.db.mapper.KCKycMainInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/9 15:59
 */
@Slf4j
@Service
public class KCAuthFlowService {


    @Resource
    private KCAuthFlowMapper kcAuthFlowMapper;

    @Resource
    private KCKycMainInfoMapper kcKycMainInfoMapper;

    public Result<PageResult<KcAuthFlowPageVO>> pageList(KcAuthFlowRequestVO requestVO) {
        LambdaQueryWrapper<KCAuthFlow> wrapper = Wrappers.lambdaQuery();
        wrapper.ge(KCAuthFlow::getCreateDate, requestVO.getAuthStartTime())
            .le(KCAuthFlow::getCreateDate, requestVO.getAuthEndTime());
        if (requestVO.getTransAmtMin() != null){
            wrapper.ge(KCAuthFlow::getTransAmt, requestVO.getTransAmtMin());
        }
        if (requestVO.getTransAmtMax() != null){
            wrapper.le(KCAuthFlow::getTransAmt, requestVO.getTransAmtMax());
        }
        if (StringUtils.isNotBlank(requestVO.getMerchantId())){
            wrapper.eq(KCAuthFlow::getMerchantId, requestVO.getMerchantId());
        }
        if (StringUtils.isNotBlank(requestVO.getTransId())){
            wrapper.eq(KCAuthFlow::getTransId, requestVO.getTransId());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthType())){
            wrapper.eq(KCAuthFlow::getAuthType, requestVO.getAuthType());
        }
        if (StringUtils.isNotBlank(requestVO.getOriginalTransId())){
            wrapper.eq(KCAuthFlow::getOriginalTransId, requestVO.getOriginalTransId());
        }
        if (StringUtils.isNotBlank(requestVO.getMerchantType())){
            wrapper.eq(KCAuthFlow::getMerchantType, requestVO.getMerchantType());
        }
        if (StringUtils.isNotBlank(requestVO.getReferenceNo())){
            wrapper.eq(KCAuthFlow::getReferenceNo, requestVO.getReferenceNo());
        }
        if (StringUtils.isNotBlank(requestVO.getCardId())){
            wrapper.eq(KCAuthFlow::getCardId, requestVO.getCardId());
        }
        if (requestVO.getAuthorizationDecision() != null){
            wrapper.eq(KCAuthFlow::getAuthorizationDecision, requestVO.getAuthorizationDecision());
        }
        if (StringUtils.isNotBlank(requestVO.getSystemsTraceAuditNumber())){
            wrapper.eq(KCAuthFlow::getSystemsTraceAuditNumber, requestVO.getSystemsTraceAuditNumber());
        }
        if (StringUtils.isNotBlank(requestVO.getChannel())){
            wrapper.eq(KCAuthFlow::getChannel, requestVO.getChannel());
        }
        if (StringUtils.isNotBlank(requestVO.getReturnCode())){
            wrapper.eq(KCAuthFlow::getReturnCode, requestVO.getReturnCode());
        }
        if (StringUtils.isNotBlank(requestVO.getApproveCode())){
            wrapper.eq(KCAuthFlow::getApproveCode, requestVO.getApproveCode());
        }
        wrapper.orderByDesc(KCAuthFlow::getCreateTime);
        PageResult<KCAuthFlow> page = PageHelperUtil.getPage(requestVO, () -> kcAuthFlowMapper.selectList(wrapper));
        // 将page的data列表商户号取出去重成List<String> memberIds
        List<Long> memberIds = page.getData().stream().map(KCAuthFlow::getMemberId).distinct().collect(Collectors.toList());
        List<KCKycMainInfo> kcKycMainInfos = memberIds.isEmpty() ? new ArrayList<>() : kcKycMainInfoMapper.selectList(
            new LambdaQueryWrapper<KCKycMainInfo>().in(KCKycMainInfo::getMemberId, memberIds));
        PageResult<KcAuthFlowPageVO> pageVOPageResult = convertToPageVO(page, kcKycMainInfos);
        return Result.success(pageVOPageResult);
    }

    private PageResult<KcAuthFlowPageVO> convertToPageVO(PageResult<KCAuthFlow> page,
        List<KCKycMainInfo> kcKycMainInfos) {
        List<KcAuthFlowPageVO> list = page.getData().stream().map(kcAuthFlow -> {
            KcAuthFlowPageVO kcAuthFlowPageVO = new KcAuthFlowPageVO();
            kcAuthFlowPageVO.setSystemMark(kcAuthFlow.getSystemMark());
            kcAuthFlowPageVO.setChannel(kcAuthFlow.getChannel());
            kcAuthFlowPageVO.setAuthStartTime(kcAuthFlow.getAuthStartTime());
            kcAuthFlowPageVO.setMerchantId(kcAuthFlow.getMerchantId());
            // 根据merchantId查询商户名称
            String merchantName = kcKycMainInfos.stream()
                .filter(info -> info.getMemberId().equals(kcAuthFlow.getMemberId()))
                .map(KCKycMainInfo::getEnglishName)
                .findFirst()
                .orElse(null);
            kcAuthFlowPageVO.setMerchantName(merchantName);
            kcAuthFlowPageVO.setCardId(kcAuthFlow.getCardId());
            kcAuthFlowPageVO.setAuthType(kcAuthFlow.getAuthType());
            kcAuthFlowPageVO.setTransCcyCode(kcAuthFlow.getCurrencyCodeTransaction());
            kcAuthFlowPageVO.setTransAmt(kcAuthFlow.getTransAmt());
            kcAuthFlowPageVO.setBillCcyCode(kcAuthFlow.getCurrencyCodeCardholderBilling());
            kcAuthFlowPageVO.setBillAmount(kcAuthFlow.getBillAmountWithMarkup());
            kcAuthFlowPageVO.setAuthorizationDecision(kcAuthFlow.getAuthorizationDecision());
            kcAuthFlowPageVO.setReferenceNo(kcAuthFlow.getReferenceNo());
            kcAuthFlowPageVO.setApproveCode(kcAuthFlow.getApproveCode());
            kcAuthFlowPageVO.setTransId(kcAuthFlow.getTransId());
            kcAuthFlowPageVO.setOriginalTransId(kcAuthFlow.getOriginalTransId());
            kcAuthFlowPageVO.setCardAcceptorName(kcAuthFlow.getCardAcceptorName());
            kcAuthFlowPageVO.setRemainAuthAmt(kcAuthFlow.getRemainAuthAmt());
            kcAuthFlowPageVO.setRemainFrozenAmt(kcAuthFlow.getRemainFrozenAmt());
            kcAuthFlowPageVO.setCreateDate(kcAuthFlow.getCreateDate());
            return kcAuthFlowPageVO;
        }).collect(Collectors.toList());
        return new PageResult<>(list, page.getPageNum(), page.getPageSize(), page.getTotal(), page.getExtraInfo());
    }

    public Result<KcAuthFlowDetailVO> detail(KcAuthFlowDetailRequestVO requestVO) {
        KCAuthFlow kcAuthFlow = kcAuthFlowMapper.selectOne(
            new LambdaQueryWrapper<KCAuthFlow>().eq(KCAuthFlow::getTransId, requestVO.getTransId())
                .eq(KCAuthFlow::getCreateDate, requestVO.getCreateDate()));
        if (kcAuthFlow == null) {
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        KCKycMainInfo kcKycMainInfo = kcKycMainInfoMapper.selectOne(
            new LambdaQueryWrapper<KCKycMainInfo>().eq(KCKycMainInfo::getMemberId, kcAuthFlow.getMemberId()));
        KcAuthFlowDetailVO kcAuthFlowDetailVO = new KcAuthFlowDetailVO();
        kcAuthFlowDetailVO.setSystemMark(kcAuthFlow.getSystemMark());
        kcAuthFlowDetailVO.setRequestId(kcAuthFlow.getRequestId());
        kcAuthFlowDetailVO.setOriginalRequestId(kcAuthFlow.getOriginalTransId());
        kcAuthFlowDetailVO.setAuthStartTime(kcAuthFlow.getAuthStartTime());
        kcAuthFlowDetailVO.setAuthCompletionTime(kcAuthFlow.getAuthCompletionTime());
        kcAuthFlowDetailVO.setMerchantId(kcAuthFlow.getMerchantId());
        kcAuthFlowDetailVO.setMerchantName(kcKycMainInfo == null ? null : kcKycMainInfo.getEnglishName());
        kcAuthFlowDetailVO.setCardId(kcAuthFlow.getCardId());
        kcAuthFlowDetailVO.setChannel(kcAuthFlow.getChannel());
        kcAuthFlowDetailVO.setTransId(kcAuthFlow.getTransId());
        kcAuthFlowDetailVO.setOriginalTransId(kcAuthFlow.getOriginalTransId());
        kcAuthFlowDetailVO.setReferenceNo(kcAuthFlow.getReferenceNo());
        kcAuthFlowDetailVO.setApproveCode(kcAuthFlow.getApproveCode());
        kcAuthFlowDetailVO.setCardAcceptorName(kcAuthFlow.getCardAcceptorName());
        kcAuthFlowDetailVO.setCardAcceptorIdentificationCode(kcAuthFlow.getCardAcceptorIdentificationCode());
        kcAuthFlowDetailVO.setCardAcceptorTerminalIdentification(kcAuthFlow.getCardAcceptorTerminalIdentification());
        kcAuthFlowDetailVO.setSystemsTraceAuditNumber(kcAuthFlow.getSystemsTraceAuditNumber());
        kcAuthFlowDetailVO.setMessageType(kcAuthFlow.getMessageType());
        kcAuthFlowDetailVO.setProcessingCode(kcAuthFlow.getProcessingCode());
        kcAuthFlowDetailVO.setSvfeTransactionType(kcAuthFlow.getSvfeTransactionType());
        kcAuthFlowDetailVO.setReturnCode(kcAuthFlow.getReturnCode());
        kcAuthFlowDetailVO.setMerchantType(kcAuthFlow.getMerchantType());
        kcAuthFlowDetailVO.setAuthType(kcAuthFlow.getAuthType());
        kcAuthFlowDetailVO.setAuthorizationDecision(kcAuthFlow.getAuthorizationDecision());
        kcAuthFlowDetailVO.setTransCcyCode(kcAuthFlow.getCurrencyCodeTransaction());
        kcAuthFlowDetailVO.setTransAmt(kcAuthFlow.getTransAmt());
        kcAuthFlowDetailVO.setBillCcyCode(kcAuthFlow.getCurrencyCodeCardholderBilling());
        kcAuthFlowDetailVO.setBillAmount(kcAuthFlow.getBillAmount());
        kcAuthFlowDetailVO.setMarkupRate(kcAuthFlow.getMarkupRate());
        kcAuthFlowDetailVO.setBillAmountWithMarkup(kcAuthFlow.getBillAmountWithMarkup());
        kcAuthFlowDetailVO.setConversionRateCardholderBilling(kcAuthFlow.getConversionRateCardholderBilling());
        kcAuthFlowDetailVO.setRemainAuthAmt(kcAuthFlow.getRemainAuthAmt());
        kcAuthFlowDetailVO.setRemainBillAmt(kcAuthFlow.getRemainBillAmtWithMarkup());
        kcAuthFlowDetailVO.setRemainFrozenAmt(kcAuthFlow.getRemainFrozenAmt());
        return Result.success(kcAuthFlowDetailVO);
    }
}
