package com.kun.linkage.auth;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Random;

public class TestMain {

    private static final String CBC_ALGORITHM = "AES/CBC/PKCS5Padding";

    private static final String IV = "1234567890000000";

    public static void main(String[] args) throws Exception {
//        StringBuffer sb = new StringBuffer();
//        Random random = new Random();
//        for (int i = 0; i < 32; i++) {
//            // 生成16进制字符
//            int randomInt = random.nextInt(16);
//            String hexChar = Integer.toHexString(randomInt);
//            sb.append(hexChar);
//        }
//        System.out.println("生成的随机16进制字符串: " + sb.toString());
        String key = "2c07608f5a486cd55e1931f35a1d8285";
        System.out.println(key.length());
        String encrypted = encryptAES("123", key);
        System.out.println("加密后的数据: " + encrypted);
        String decrypted = decryptAES(encrypted, key);
        System.out.println("解密后的数据: " + decrypted);
    }

    private static String encryptAES(String data, String key) throws Exception {
        try {
//            System.out.println("开始AES加密数据: " + data);

            // aes
            //            String aeskey = "2c07608f5a486cd55e1931f35a1d8285";
            byte[] aesKey = Base64.decode(key);
            SecretKeySpec secretKey = new SecretKeySpec(aesKey, "AES");
            Cipher cipher = Cipher.getInstance(CBC_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
//            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(IV.getBytes(StandardCharsets.UTF_8)));
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            String s = java.util.Base64.getEncoder().encodeToString(encryptedBytes);
//            System.out.println("AES加密数据: " + s);
            return s;
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }

    }

    private static String decryptAES(String data, String key) throws Exception {
        try {
//            System.out.println("开始AES解密数据: " + data);

            // aes
            //            String aeskey = "2c07608f5a486cd55e1931f35a1d8285";
            byte[] aesKey = Base64.decode(key);
            SecretKeySpec secretKey = new SecretKeySpec(aesKey, "AES");
            Cipher cipher = Cipher.getInstance(CBC_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
//            cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(IV.getBytes(StandardCharsets.UTF_8)));
            byte[] decryptedBytes = cipher.doFinal(java.util.Base64.getDecoder().decode(data));
            String s = new String(decryptedBytes, StandardCharsets.UTF_8);
//            System.out.println("AES解密数据: " + s);
            return s;
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败", e);
        }
    }
}
