package com.kun.linkage.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.OrganizationAccountingDetail;
import com.kun.linkage.common.db.mapper.OrganizationAccountingDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class OrganizationAccountingDetailService {

    @Resource
    private OrganizationAccountingDetailMapper organizationAccountingDetailMapper;

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void insertOrganizationAccountingDetail(OrganizationAccountingDetail organizationTransAccounting) {
        int insert = organizationAccountingDetailMapper.insert(organizationTransAccounting);
        if (insert <= 0) {
            log.error("插入记账记录失败, 组织号: {}, 交易ID: {}", organizationTransAccounting.getOrganizationNo(),
                organizationTransAccounting.getBizId());
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateRequestNo(OrganizationAccountingDetail merchantAccountingDetail) {
        OrganizationAccountingDetail updateDetail = new OrganizationAccountingDetail();
        updateDetail.setRequestNo(merchantAccountingDetail.getRequestNo());
        int updateCount = organizationAccountingDetailMapper.update(updateDetail,
            new LambdaQueryWrapper<OrganizationAccountingDetail>().eq(OrganizationAccountingDetail::getId,
                    merchantAccountingDetail.getId())
                .eq(OrganizationAccountingDetail::getCreateTime, merchantAccountingDetail.getCreateTime()));
        log.info("更新记账请求流水号, 记账ID: {}, 更新条数: {}", merchantAccountingDetail.getId(), updateCount);
        if (updateCount <= 0) {
            log.error("更新记账请求流水号失败, 记账ID: {}", merchantAccountingDetail.getId());
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateAccountingResult(OrganizationAccountingDetail merchantAccountingDetail) {
        OrganizationAccountingDetail updateDetail = new OrganizationAccountingDetail();
        updateDetail.setAccountingStatus(merchantAccountingDetail.getAccountingStatus());
        updateDetail.setLastModifyTime(merchantAccountingDetail.getLastModifyTime());
        updateDetail.setFailMessage(merchantAccountingDetail.getFailMessage());
        updateDetail.setAccountingReversalStatus(merchantAccountingDetail.getAccountingReversalStatus());
        int updateCount = organizationAccountingDetailMapper.update(updateDetail,
            new LambdaQueryWrapper<OrganizationAccountingDetail>().eq(OrganizationAccountingDetail::getId,
                    merchantAccountingDetail.getId())
                .eq(OrganizationAccountingDetail::getCreateTime, merchantAccountingDetail.getCreateTime()));
        log.info("更新记账状态, 机构号: {}, 交易ID: {}, 记账ID: {}, 更新条数: {}",
            merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getBizId(),
            merchantAccountingDetail.getId(), updateCount);
        if (updateCount <= 0) {
            log.error("更新记账状态失败, 机构号: {}, 交易ID: {}, 记账ID: {}",
                merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getBizId(),
                merchantAccountingDetail.getId());
            // 如果更新失败,强行修改数据状态为失败，根据是否需要冲账继续后续流程
            merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateAccountingReversalPendingStatus(OrganizationAccountingDetail merchantAccountingDetail) {
        merchantAccountingDetail.setAccountingReversalStatus(OperationStatusEnum.PENDING.getStatus());
        OrganizationAccountingDetail updateDetail = new OrganizationAccountingDetail();
        updateDetail.setAccountingReversalStatus(merchantAccountingDetail.getAccountingReversalStatus());
        updateDetail.setAccountingReversalCount(merchantAccountingDetail.getAccountingReversalCount());
        int updateCount = organizationAccountingDetailMapper.update(updateDetail,
            new LambdaQueryWrapper<OrganizationAccountingDetail>().eq(OrganizationAccountingDetail::getId,
                    merchantAccountingDetail.getId())
                .eq(OrganizationAccountingDetail::getCreateTime, merchantAccountingDetail.getCreateTime()));
        log.info("更新冲账状态为处理中, 机构号: {}, 交易ID: {}, 记账ID: {}, 更新条数: {}",
            merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getBizId(),
            merchantAccountingDetail.getId(), updateCount);
        if (updateCount <= 0) {
            log.error("更新冲账状态失败, 机构号: {}, 交易ID: {}, 记账ID: {}",
                merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getBizId(),
                merchantAccountingDetail.getId());
            // 如果更新失败,强行修改数据状态为失败，根据是否需要冲账继续后续流程
            merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
        }
    }

    /**
     * 根据记账ID和创建时间查询记账明细
     *
     * @param organizationAccountingDetailId 记账ID
     * @param createTime                     创建时间
     * @return 记账明细
     */
    public OrganizationAccountingDetail selectDataByIdAndCreateTime(String organizationAccountingDetailId,
        Date createTime) {
        return this.organizationAccountingDetailMapper.selectOne(
            new LambdaQueryWrapper<OrganizationAccountingDetail>().eq(OrganizationAccountingDetail::getId,
                organizationAccountingDetailId).eq(OrganizationAccountingDetail::getCreateTime, createTime));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateAccountingReversalResult(OrganizationAccountingDetail merchantAccountingDetail) {
        try {
            OrganizationAccountingDetail updateDetail = new OrganizationAccountingDetail();
            updateDetail.setLastModifyTime(merchantAccountingDetail.getLastModifyTime());
            updateDetail.setAccountingReversalStatus(merchantAccountingDetail.getAccountingReversalStatus());
            merchantAccountingDetail.setAccountingReversalCount(merchantAccountingDetail.getAccountingReversalCount()+1);
            updateDetail.setAccountingReversalCount(merchantAccountingDetail.getAccountingReversalCount());
            int updateCount = organizationAccountingDetailMapper.update(updateDetail,
                new LambdaQueryWrapper<OrganizationAccountingDetail>().eq(OrganizationAccountingDetail::getId,
                        merchantAccountingDetail.getId())
                    .eq(OrganizationAccountingDetail::getCreateTime, merchantAccountingDetail.getCreateTime()));
            log.info("更新冲账状态, 机构号: {}, 交易ID: {}, 记账ID: {}, 更新条数: {}",
                merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getBizId(),
                merchantAccountingDetail.getId(), updateCount);
        } catch (Exception e) {
            log.error("更新冲账状态异常", e);
        }
    }
}
