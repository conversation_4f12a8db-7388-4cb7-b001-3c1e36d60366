package com.kun.linkage.auth.service;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.auth.facade.constant.AuthExportConstant;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationExportRequestVO;
import com.kun.linkage.common.base.enums.ExportFileTypeEnum;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthFlow;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.mapper.AuthFlowMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * 授权记录导出服务
 */
@Slf4j
@Service
public class AuthorizationExportService {

    @Resource
    private AuthFlowMapper authFlowMapper;

    @Resource
    private ExportFileRecordService exportFileRecordService;

    @Resource
    private AwsS3Util awsS3Util;

    @Resource
    private AwzS3Properties awzS3Properties;

    @Value("${kun.aws.s3.fileFolder:}")
    private String fileFolder;

    /**
     * 异步导出授权记录数据
     */
    @Async("externalApiAsyncExecutor")
    public void asyncExportData(KLExportFileRecord fileRecord, AuthorizationExportRequestVO requestVO) {
        String fileRecordId = fileRecord.getFileRecordId();
        try {
            log.info("开始异步导出授权记录数据，文件记录ID: {}", fileRecordId);

            // 1. 查询数据
            List<AuthFlow> dataList = queryExportData(requestVO);
            log.info("查询到 {} 条授权记录数据", dataList.size());

            // 2. 生成CSV文件
            File csvFile = generateCsvFile(dataList, fileRecord.getFileName());
            log.info("CSV文件生成完成，文件大小: {} bytes", csvFile.length());

            // 3. 上传到S3
            String s3Url = uploadToS3(csvFile, fileRecord.getFileName());
            log.info("文件上传S3成功，URL: {}", s3Url);

            // 4. 更新文件记录为成功状态
            exportFileRecordService.updateFileRecordSuccess(fileRecordId, s3Url, csvFile.length());

            // 5. 清理临时文件
            if (csvFile.exists()) {
                csvFile.delete();
                log.info("临时文件已清理: {}", csvFile.getAbsolutePath());
            }
            log.info("授权记录数据导出完成，文件记录ID: {}", fileRecordId);
        } catch (Exception e) {
            log.error("授权记录数据导出失败，文件记录ID: {}, 错误: {}", fileRecordId, e.getMessage(), e);
            exportFileRecordService.updateFileRecordFailed(fileRecordId, e.getMessage());
        }
    }

    /**
     * 查询导出数据
     */
    private List<AuthFlow> queryExportData(AuthorizationExportRequestVO requestVO) {
        LambdaQueryWrapper<AuthFlow> wrapper = buildQueryWrapper(requestVO);
        return authFlowMapper.selectList(wrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<AuthFlow> buildQueryWrapper(AuthorizationExportRequestVO requestVO) {
        LambdaQueryWrapper<AuthFlow> wrapper = Wrappers.lambdaQuery();

        // 授权日期范围（必填）
        wrapper.ge(AuthFlow::getCreateTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getAuthorizationDateFrom().trim() + " 00:00:00", DateUtils.DATETIME_PATTERN)));
        wrapper.le(AuthFlow::getCreateTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getAuthorizationDateUntil().trim() + " 23:59:59", DateUtils.DATETIME_PATTERN)));

        // 交易金额范围
        if (requestVO.getTransAmountFrom() != null) {
            wrapper.ge(AuthFlow::getTransAmount, requestVO.getTransAmountFrom());
        }
        if (requestVO.getTransAmountTo() != null) {
            wrapper.le(AuthFlow::getTransAmount, requestVO.getTransAmountTo());
        }

        // 其他查询条件
        if (StringUtils.isNotBlank(requestVO.getMerchantNo())) {
            wrapper.eq(AuthFlow::getMerchantNo, requestVO.getMerchantNo());
        }
        if (StringUtils.isNotBlank(requestVO.getAcquirerMerchantNo())) {
            wrapper.eq(AuthFlow::getCardAcceptorId, requestVO.getAcquirerMerchantNo());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthTransId())) {
            wrapper.eq(AuthFlow::getId, requestVO.getAuthTransId());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthTransType())) {
            wrapper.eq(AuthFlow::getTransType, requestVO.getAuthTransType());
        }
        if (StringUtils.isNotBlank(requestVO.getOriginalAuthTransId())) {
            wrapper.eq(AuthFlow::getOriginalId, requestVO.getOriginalAuthTransId());
        }
        if (StringUtils.isNotBlank(requestVO.getAcquirerMerchantMcc())) {
            wrapper.eq(AuthFlow::getMcc, requestVO.getAcquirerMerchantMcc());
        }
        if (StringUtils.isNotBlank(requestVO.getAcquireReferenceNo())) {
            wrapper.eq(AuthFlow::getAcquireReferenceNo, requestVO.getAcquireReferenceNo());
        }
        if (StringUtils.isNotBlank(requestVO.getGatewayCardId())) {
            wrapper.eq(AuthFlow::getGatewayCardId, requestVO.getGatewayCardId());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthStatus())) {
            wrapper.eq(AuthFlow::getStatus, requestVO.getAuthStatus());
        }
        if (StringUtils.isNotBlank(requestVO.getResponseCode())) {
            wrapper.eq(AuthFlow::getResponseCode, requestVO.getResponseCode());
        }
        if (StringUtils.isNotBlank(requestVO.getSystemsTraceAuditNumber())) {
            wrapper.eq(AuthFlow::getSystemsTraceAuditNumber, requestVO.getSystemsTraceAuditNumber());
        }
        if (StringUtils.isNotBlank(requestVO.getApproveCode())) {
            wrapper.eq(AuthFlow::getApproveCode, requestVO.getApproveCode());
        }
        if (StringUtils.isNotBlank(requestVO.getProcessor())) {
            wrapper.eq(AuthFlow::getProcessor, requestVO.getProcessor());
        }
        if (StringUtils.isNotBlank(requestVO.getCardNoSuffix())) {
            wrapper.eq(AuthFlow::getMaskedCardNo, requestVO.getCardNoSuffix());
        }

        wrapper.orderByDesc(AuthFlow::getCreateTime);
        return wrapper;
    }

    /**
     * 生成CSV文件
     */
    private File generateCsvFile(List<AuthFlow> dataList, String fileName) throws IOException {
        File tempFile = File.createTempFile("auth_export_", ".csv");

        try (FileOutputStream fos = new FileOutputStream(tempFile);
             OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
             CSVPrinter csvPrinter = new CSVPrinter(osw, CSVFormat.DEFAULT.withHeader(AuthExportConstant.CSV_HEADERS))) {

            // 写入UTF-8 BOM头，确保中文正确显示
            fos.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            // 写入数据行
            for (AuthFlow authFlow : dataList) {
                csvPrinter.printRecord(
                        formatDateTime(authFlow.getCreateTime()),           // 授权日期时间
                        formatText(authFlow.getId()),                                   // 系统授权号
                        formatText(authFlow.getMerchantNo()),                          // 商户号
                        formatText(authFlow.getGatewayCardId()),                       // Card ID
                        authFlow.getTransType(),                           // 授权类型
                        authFlow.getTransCurrency(),                       // 交易币种
                        formatAmount(authFlow.getTransAmount()),           // 交易金额
                        authFlow.getCardholderBillingCurrency(),           // 持卡人币种
                        formatAmount(authFlow.getCardholderBillingAmount()), // 持卡人金额
                        authFlow.getStatus(),                              // 授权状态
                        formatText(authFlow.getAcquireReferenceNo()),                  // 参考号
                        authFlow.getApproveCode(),                         // 授权码
                        formatText(authFlow.getOriginalId()),                          // 原系统授权号
                        authFlow.getCardAcceptorName(),                    // 收单商户名称
                        authFlow.getMcc(),                                 // 收单商户MCC
                        authFlow.getClearFlag()                            // 清算状态
                );
            }
        }

        return tempFile;
    }

    /**
     * 上传文件到S3
     */
    private String uploadToS3(File file, String fileName) {
        return awsS3Util.uploadChunkedFile(
                fileName,
                file,
                awzS3Properties.getBucket(),
                fileFolder + "/" + AuthExportConstant.ORG_EXPORT_FILES_ROOT_DIR + "/" + ExportFileTypeEnum.AUTHORIZATION_EXPORT.getFileDir(),
                CannedAccessControlList.PublicReadWrite
        );
    }

    /**
     * 格式化可能被识别为数字的字段，防止Excel显示为科学计数法
     */
    private String formatText(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        // 在值前添加制表符，强制Excel将其作为文本处理
        return "\t" + value;
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(Date date) {
        if (date == null) {
            return "";
        }
        return DateUtils.formatDate(date, DateUtils.DATETIME_PATTERN);
    }

    /**
     * 格式化金额
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "";
        }
        return amount.toString();
    }

    /**
     * 生成文件名
     */
    public String generateFileName(String merchantNo) {
        String merchant = StringUtils.isNotBlank(merchantNo) ? merchantNo : AuthExportConstant.DEFAULT_MERCHANT_NO;
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format(ExportFileTypeEnum.AUTHORIZATION_EXPORT.getFileNameFormat(), merchant, timestamp);
    }
}
