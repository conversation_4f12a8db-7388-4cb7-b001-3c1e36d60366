package com.kun.linkage.customer.facade.dto.organization.basic;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.customer.facade.enums.OrganizationModeEnum;
import com.kun.linkage.customer.facade.enums.OrganizationPoolCurrencyCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 机构信息bean
 */
public class OrganizationBasicInfoBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationName;

    /**
     * 状态
     */
    @Schema(description = "状态;Valid:有效,Invalid:无效")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = ValidStatusEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String status;

    /**
     * kun对应的商户号
     */
    @Schema(description = "kun对应的商户号")
    private String kunMid;

    /**
     * payx对应的商户号
     */
    @Schema(description = "payx对应的商户号")
    private String payxMid;

    @Schema(description = "模式")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = OrganizationModeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String mode;

    /**
     * 是否校验机构账户标记;0否;1是
     */
    @Schema(description = "是否校验机构账户标记;0:否,1:是")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer checkOrganizationAccountFlag;

    /**
     * 是否校验机构下用户账户标记;0否;1是
     */
    @Schema(description = "是否校验机构下用户账户标记;0:否,1:是")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer checkCustomerAccountFlag;

    /**
     * 是否个人客户kyc报送;0否;1:是
     */
    @Schema(description = "是否个人客户kyc报送;0否;1:是")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer isKycReported;

    /**
     * 是否个人客户kyc校验;0否;1:是
     */
    @Schema(description = "是否个人客户kyc校验;0否;1:是")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer isKycVerified;

    /**
     * 第三方授权标记
     */
    @Schema(description = "第三方授权标记;0:否,1:是")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer thirdPartyAuthorizationFlag;

    /**
     * MPC租户ID
     */
    @Schema(description = "MPC租户ID")
    @Length(max = 64, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String mpcTenantId;

    /**
     * MPC集团号
     */
    @Schema(description = "MPC集团号")
    @Length(max = 16, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String mpcGroupCode;

    /**
     * MPC TOKEN
     */
    @Schema(description = "MPC TOKEN")
    @Length(max = 64, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String mpcToken;

    @Schema(description = "资金池币种;字典:KL_ORGANIZATION_POOL_CCY")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = OrganizationPoolCurrencyCodeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String poolCurrencyCode;

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getKunMid() {
        return kunMid;
    }

    public void setKunMid(String kunMid) {
        this.kunMid = kunMid;
    }
    public String getPayxMid() {
        return payxMid;
    }

    public void setPayxMid(String payxMid) {
        this.payxMid = payxMid;
    }
    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
    public Integer getCheckOrganizationAccountFlag() {
        return checkOrganizationAccountFlag;
    }

    public void setCheckOrganizationAccountFlag(Integer checkOrganizationAccountFlag) {
        this.checkOrganizationAccountFlag = checkOrganizationAccountFlag;
    }
    public Integer getCheckCustomerAccountFlag() {
        return checkCustomerAccountFlag;
    }

    public void setCheckCustomerAccountFlag(Integer checkCustomerAccountFlag) {
        this.checkCustomerAccountFlag = checkCustomerAccountFlag;
    }

    public Integer getIsKycReported() {
        return isKycReported;
    }

    public void setIsKycReported(Integer isKycReported) {
        this.isKycReported = isKycReported;
    }

    public Integer getIsKycVerified() {
        return isKycVerified;
    }

    public void setIsKycVerified(Integer isKycVerified) {
        this.isKycVerified = isKycVerified;
    }

    public Integer getThirdPartyAuthorizationFlag() {
        return thirdPartyAuthorizationFlag;
    }

    public void setThirdPartyAuthorizationFlag(Integer thirdPartyAuthorizationFlag) {
        this.thirdPartyAuthorizationFlag = thirdPartyAuthorizationFlag;
    }

    public String getMpcTenantId() {
        return mpcTenantId;
    }

    public void setMpcTenantId(String mpcTenantId) {
        this.mpcTenantId = mpcTenantId;
    }

    public String getMpcGroupCode() {
        return mpcGroupCode;
    }

    public void setMpcGroupCode(String mpcGroupCode) {
        this.mpcGroupCode = mpcGroupCode;
    }

    public String getMpcToken() {
        return mpcToken;
    }

    public void setMpcToken(String mpcToken) {
        this.mpcToken = mpcToken;
    }

    public String getPoolCurrencyCode() {
        return poolCurrencyCode;
    }

    public void setPoolCurrencyCode(String poolCurrencyCode) {
        this.poolCurrencyCode = poolCurrencyCode;
    }
}
