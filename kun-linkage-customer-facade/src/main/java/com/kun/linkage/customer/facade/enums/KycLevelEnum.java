package com.kun.linkage.customer.facade.enums;

public enum KycLevelEnum {

    NONE("NONE", "无",null),
    LEVEL_0("LEVEL_0", "零级",0),
    LEVEL_1("LEVEL_1", "一级",1),
    LEVEL_2("LEVEL_2", "二级",2),
    LEVEL_3("LEVEL_3", "三级",3);

    private final String value;
    private final String label;
    private final Integer level;
    private final String dictType = "KYC_LEVEL";

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public Integer getLevel() {
        return level;
    }


    KycLevelEnum(String value, String label, Integer level) {
        this.value = value;
        this.label = label;
        this.level = level;
    }

    public static KycLevelEnum fromValue(String value) {
        for (KycLevelEnum level : values()) {
            if (level.getValue().equalsIgnoreCase(value))
                return level;
        }

        return null;
    }
}


