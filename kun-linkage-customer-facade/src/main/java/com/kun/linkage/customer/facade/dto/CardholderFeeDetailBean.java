package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.customer.facade.enums.CardholderFeeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 持卡人费率明细bean
 */
public class CardholderFeeDetailBean implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手续费类型
     */
    @Schema(description = "手续费类型:字典:KL_CARDHOLDER_FEE_TYPE")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = CardholderFeeTypeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String feeType;

    /**
     * 金额区间:最小金额
     */
    @Schema(description = "状态;Valid:有效,Invalid:无效")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.00", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal minAmount;

    /**
     * 金额区间:最大金额
     */
    @Schema(description = "金额区间:最大金额")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.00", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal maxAmount;

    /**
     * 划线费率-比例
     */
    @Schema(description = "划线费率-比例(前端展示需乘以100,送后端需除以100)")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.0000", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @DecimalMax(value = "1.0000", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal scribingProportionRate;

    /**
     * 划线费率-比例的保底金额
     */
    @Schema(description = "划线费率-比例的保底金额")
    private BigDecimal scribingProportionMinAmount;

    /**
     * 划线费率-比例的封顶金额
     */
    @Schema(description = "划线费率-比例的封顶金额")
    private BigDecimal scribingProportionMaxAmount;

    /**
     * 划线费率-固定值
     */
    @Schema(description = "划线费率-固定值")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.00", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal scribingFixedAmount;

    /**
     * 实际费率-比例
     */
    @Schema(description = "实际费率-比例(前端展示需乘以100,送后端需除以100)")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.0000", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @DecimalMax(value = "1.0000", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal actualProportionRate;

    /**
     * 实际费率-比例的保底金额
     */
    @Schema(description = "实际费率-比例的保底金额")
    private BigDecimal actualProportionMinAmount;

    /**
     * 实际费率-比例的封顶金额
     */
    @Schema(description = "实际费率-比例的封顶金额")
    private BigDecimal actualProportionMaxAmount;

    /**
     * 实际费率-固定值
     */
    @Schema(description = "实际费率-固定值")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.00", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal actualFixedAmount;

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public BigDecimal getScribingProportionRate() {
        return scribingProportionRate;
    }

    public void setScribingProportionRate(BigDecimal scribingProportionRate) {
        this.scribingProportionRate = scribingProportionRate;
    }

    public BigDecimal getScribingProportionMinAmount() {
        return scribingProportionMinAmount;
    }

    public void setScribingProportionMinAmount(BigDecimal scribingProportionMinAmount) {
        this.scribingProportionMinAmount = scribingProportionMinAmount;
    }

    public BigDecimal getScribingProportionMaxAmount() {
        return scribingProportionMaxAmount;
    }

    public void setScribingProportionMaxAmount(BigDecimal scribingProportionMaxAmount) {
        this.scribingProportionMaxAmount = scribingProportionMaxAmount;
    }

    public BigDecimal getScribingFixedAmount() {
        return scribingFixedAmount;
    }

    public void setScribingFixedAmount(BigDecimal scribingFixedAmount) {
        this.scribingFixedAmount = scribingFixedAmount;
    }

    public BigDecimal getActualProportionRate() {
        return actualProportionRate;
    }

    public void setActualProportionRate(BigDecimal actualProportionRate) {
        this.actualProportionRate = actualProportionRate;
    }

    public BigDecimal getActualProportionMinAmount() {
        return actualProportionMinAmount;
    }

    public void setActualProportionMinAmount(BigDecimal actualProportionMinAmount) {
        this.actualProportionMinAmount = actualProportionMinAmount;
    }

    public BigDecimal getActualProportionMaxAmount() {
        return actualProportionMaxAmount;
    }

    public void setActualProportionMaxAmount(BigDecimal actualProportionMaxAmount) {
        this.actualProportionMaxAmount = actualProportionMaxAmount;
    }

    public BigDecimal getActualFixedAmount() {
        return actualFixedAmount;
    }

    public void setActualFixedAmount(BigDecimal actualFixedAmount) {
        this.actualFixedAmount = actualFixedAmount;
    }
}
