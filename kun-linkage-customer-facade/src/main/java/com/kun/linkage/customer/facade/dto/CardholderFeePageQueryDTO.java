package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 分页查询持卡人费率信息参数
 */
public class CardholderFeePageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    private String organizationNo;

    /**
     * 卡产品编号
     */
    @Schema(description = "卡产品编号")
    private String cardProductCode;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
