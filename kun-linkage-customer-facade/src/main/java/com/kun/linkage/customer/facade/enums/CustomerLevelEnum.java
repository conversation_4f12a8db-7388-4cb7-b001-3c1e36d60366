package com.kun.linkage.customer.facade.enums;

/**
 * 客户等级枚举
 */
public enum CustomerLevelEnum {
    LEVEL0(0, "等级0"),
    LEVEL1(1, "等级1"),
    LEVEL2(2, "等级2"),
    LEVEL3(3, "等级3")
    ;

    CustomerLevelEnum(Integer level, String desc) {
        this.level = level;
        this.desc = desc;
    }

    private final Integer level;

    private final String desc;

    public Integer getLevel() {
        return level;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomerLevelEnum getByLevel(Integer level) {
        for (CustomerLevelEnum customerLevelEnum : CustomerLevelEnum.values()) {
            if (customerLevelEnum.getLevel().equals(level)) {
                return customerLevelEnum;
            }
        }
        return null;
    }

    public static boolean contains(Integer level) {
        for (CustomerLevelEnum customerLevelEnum : CustomerLevelEnum.values()) {
            if (customerLevelEnum.getLevel().equals(level)) {
                return true;
            }
        }
        return false;
    }
}
