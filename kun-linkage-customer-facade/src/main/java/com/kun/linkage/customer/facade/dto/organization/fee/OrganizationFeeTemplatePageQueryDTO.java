package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 分页查询机构费用模版参数
 */
public class OrganizationFeeTemplatePageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模版号
     */
    @Schema(description = "模版号")
    private String templateNo;

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }
}
