package com.kun.linkage.customer.facade.dto.organization.account;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

/**
 * 机构账户信息提交DTO
 */
public class OrganizationAccountInfoModifySubmitDTO extends OrganizationAccountInfoBean {
    /**
     * 机构账户信息ID(修改时需上送)
     */
    @Schema(description = "机构账户信息ID(修改时需上送)")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Long organizationAccountInfoId;

    public Long getOrganizationAccountInfoId() {
        return organizationAccountInfoId;
    }

    public void setOrganizationAccountInfoId(Long organizationAccountInfoId) {
        this.organizationAccountInfoId = organizationAccountInfoId;
    }
}
