package com.kun.linkage.customer.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 技术参数更新DTO
 *
 * @since 2025-07-28
 */
@Data
@Schema(description = "技术参数更新DTO")
public class TechnicalParamsUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "机构编号", required = true)
    @NotBlank(message = "机构编号不能为空")
    private String organizationNo;

    @Schema(description = "webhook开关状态：0-关闭，1-开启", required = true)
    @NotNull(message = "webhook开关状态不能为空")
    private Integer webhookEnabled;

    @Schema(description = "WebHook地址-KYC")
    private String webhookUrlKyc;

    @Schema(description = "WebHook地址-转三方授权")
    private String webhookUrlThirdPartyAuth;

    @Schema(description = "WebHook地址-授权交易结果")
    private String webhookUrlAuthResult;

    @Schema(description = "WebHook地址-3DS OTP")
    private String webhookUrl3dsOtp;

    @Schema(description = "状态")
    private String status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private String createUserId;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createUserName;

    /**
     * 最后一次修改时间
     */
    @Schema(description = "最后一次修改时间")
    private LocalDateTime lastModifyTime;

    /**
     * 最后一次修改人id
     */
    @Schema(description = "最后一次修改人id")
    private String lastModifyUserId;

    /**
     * 最后一次修改人名称
     */
    @Schema(description = "最后一次修改人名称")
    private String lastModifyUserName;
}
