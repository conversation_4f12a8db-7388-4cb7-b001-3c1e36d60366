package com.kun.linkage.customer.facade.constants;

/**
 * 客户服务相关错误码
 */
public class CustomerTipConstant {
    /**
     * 机构信息找不到
     */
    public static final String ORGANIZATION_NOT_FOUND = "KLU0001";
    /**
     * 卡产品信息找不到
     */
    public static final String CARD_PRODUCT_NOT_FOUND = "KLU0002";
    /**
     * 机构未开通卡产品
     */
    public static final String ORGANIZATION_HAS_NOT_ACTIVATED_CARD_PRODUCT = "KLU0003";
    /**
     * 存在处理中的开卡记录
     */
    public static final String ALREADY_EXIST_PENDING_OPEN_CARD_RECORD = "KLU0004";
    /**
     * 已存在卡信息
     */
    public static final String ALREADY_EXIST_CARD_INFO = "KLU0005";
    /**
     * 卡信息不存在
     */
    public static final String CARD_INFO_NOT_FOUND = "KLU0006";
    /**
     * 卡已注销
     */
    public static final String CARD_HAS_BEEN_CANCELLED = "KLU0007";
    /**
     * 卡状态必须为正常
     */
    public static final String CARD_STATUS_MUST_BE_NORMAL = "KLU0008";
    /**
     * 卡状态必须为冻结
     */
    public static final String CARD_STATUS_MUST_BE_FREEZE = "KLU0009";
    /**
     * 调用渠道失败
     */
    public static final String CALL_CHANNEL_FAIL = "KLU0010";
    /**
     * 开卡操作记录不存在
     */
    public static final String OPEN_CARD_OPERATOR_RECORD_NOT_FOUND = "KLU0011";
    /**
     * 卡产品信息特殊用法不能为空
     */
    public static final String CARD_PRODUCT_SPECIAL_USAGE_CANNOT_BE_EMPTY = "KLU0012";
    /**
     * 该渠道卡片无需激活
     */
    public static final String CHANNEL_CARD_NOT_REQUIRE_ACTIVATION = "KLU0013";
    /**
     * 钱包已存在
     */
    public static final String WALLET_ALREADY_EXIST = "KLU0014";
    /**
     * 机构MPC配置不存在
     */
    public static final String ORGANIZATION_MPC_CONFIG_NOT_FOUND = "KLU0015";
    /**
     * 时间区间不能重叠
     */
    public static final String TIME_INTERVALS_CANNOT_OVERLAP = "KLU0016";
    /**
     * 持卡人费率不存在
     */
    public static final String CARDHOLDER_FEE_NOT_FOUND = "KLU0017";
    /**
     * 客户信息不存在
     */
    public static final String CUSTOMER_INFO_NOT_FOUND = "KLU0018";
    /**
     * 机构状态不正常
     */
    public static final String ORGANIZATION_STATUS_IS_NOT_NORMAL = "KLU0019";
    /**
     * 卡未激活
     */
    public static final String CARD_IS_NOT_ACTIVATED = "KLU0020";
    /**
     * 充值币种必须为卡币种
     */
    public static final String RECHARGE_CURRENCY_MUST_BE_THE_CARD_CURRENCY = "KLU0021";
    /**
     * 币种不支持
     */
    public static final String CURRENCY_NOT_SUPPORT = "KLU0022";

    /**
     * 证件信息存在
     */
    public static final String CERTIFICATE_INFORMATION_EXISTS = "KLU0023";
    /**
     * 邮箱存在
     */
    public static final String EMAIL_EXISTS = "KLU0024";
    /**
     * 手机区号+手机号存在
     */
    public static final String MOBILE_EXISTS = "KLU0025";
    /**
     * 费率模版不存在
     */
    public static final String FEE_TEMPLATE_NOT_FOUND = "KLU0026";
    /**
     * 机构费率配置不存在
     */
    public static final String ORGANIZATION_FEE_CONFIG_NOT_FOUND = "KLU0027";
    /**
     * 机构不需要报送kyc信息
     */
    public static final String ORGANIZATION_KYC_NO_REPORTED = "KLU0028";
    /**
     * 重复报送
     */
    public static final String ORGANIZATION_KYC_REPEATED_SUBMISSION = "KLU0029";
    /**
     * 报送信息不存在
     */
    public static final String KYC_SUBMISSION_NOT_FOUND = "KLU0030";
    /**
     * KYC 报送信息不一致
     */
    public static final String KYC_SUBMISSION_INCONSISTENT_INFORMATION = "KLU0031";
    /**
     * 机构资金池币种未配置
     */
    public static final String ORGANIZATION_POOL_CURRENCY_CODE_NOT_CONFIG = "KLU0032";
    /**
     * 机构账户余额不足
     */
    public static final String ORGANIZATION_INSUFFICIENT_ACCOUNT_BALANCE = "KLU0033";
    /**
     * 机构账户不存在
     */
    public static final String ORGANIZATION_ACCOUNT_NOT_FOUND = "KLU0034";
    /**
     * 账户可用余额大于等于0
     */
    public static final String ACCOUNT_AVAILABLE_AMOUNT_GE_ZERO = "KLU0035";
}
