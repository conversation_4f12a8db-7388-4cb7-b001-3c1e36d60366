package com.kun.linkage.customer.facade.enums;

/**
 * 机构费用收取方式枚举
 */
public enum OrganizationFeeCollectionMethodEnum {
    /**
     * 实时
     */
    REAL_TIME("REAL_TIME", "实时"),
    /**
     * 月结
     */
    MONTHLY_SETTLEMENT("MONTHLY_SETTLEMENT", "月结"),
    ;

    private final String value;
    private final String desc;
    private final String dictType = "KL_ORG_FEE_COLLECTION_METHOD";

    OrganizationFeeCollectionMethodEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrganizationFeeCollectionMethodEnum getEnumByValue(String value) {
        for (OrganizationFeeCollectionMethodEnum itemEnum : OrganizationFeeCollectionMethodEnum.values()) {
            if (itemEnum.getValue().equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
}
