package com.kun.linkage.customer.facade.enums;

public enum CardStatusEnum {
    NORMAL("NORMAL", "正常"),
    FREEZE("FREEZE", "冻结"),
    CANCEL("CANCEL", "注销");

    private final String status;
    private final String desc;

    CardStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static CardStatusEnum getEnumByStatus(String status) {
        for (CardStatusEnum operationStatusEnum : CardStatusEnum.values()) {
            if (operationStatusEnum.getStatus().equals(status)) {
                return operationStatusEnum;
            }
        }
        return null;
    }

    public static boolean contains(String status) {
        return getEnumByStatus(status) != null;
    }
}
