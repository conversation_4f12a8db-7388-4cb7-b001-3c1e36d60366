package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.DirectionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 业务账户属性提交DTO
 */
public class BusinessAccountAttributeModifySubmitDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务账户属性信息Id
     */
    @Schema(description = "业务账户属性信息Id")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Long businessAccountAttributeId;

    /**
     * 方向;C: Credit;D: Debit
     */
    @Schema(description = "方向;C: Credit;D: Debit")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = DirectionEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String direction;

    /**
     * Topup Flag;0:not allow;1: allow
     */
    @Schema(description = "Topup Flag;0:not allow;1: allow")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer topupFlag;

    /**
     * Withdrawal Flag;0:not allow;1: allow
     */
    @Schema(description = "Withdrawal Flag;0:not allow;1: allow")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer withdrawalFlag;

    /**
     * Transfer Out Flag;0:not allow;1: allow
     */
    @Schema(description = "Transfer Out Flag;0:not allow;1: allow")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer transferOutFlag;

    public Long getBusinessAccountAttributeId() {
        return businessAccountAttributeId;
    }

    public void setBusinessAccountAttributeId(Long businessAccountAttributeId) {
        this.businessAccountAttributeId = businessAccountAttributeId;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public Integer getTopupFlag() {
        return topupFlag;
    }

    public void setTopupFlag(Integer topupFlag) {
        this.topupFlag = topupFlag;
    }

    public Integer getWithdrawalFlag() {
        return withdrawalFlag;
    }

    public void setWithdrawalFlag(Integer withdrawalFlag) {
        this.withdrawalFlag = withdrawalFlag;
    }

    public Integer getTransferOutFlag() {
        return transferOutFlag;
    }

    public void setTransferOutFlag(Integer transferOutFlag) {
        this.transferOutFlag = transferOutFlag;
    }
}
