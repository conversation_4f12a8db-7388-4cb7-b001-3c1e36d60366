package com.kun.linkage.customer.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.customer.facade.api.bean.req.ModifyCustometLevelReq;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/api/customerManagement", configuration = FeignConfiguration.class)
public interface CustomerManagementFacade {

    @Operation(description = "修改客户等级")
    @RequestMapping(value = "/modifyCustomerLevel", method = RequestMethod.POST)
    Result<Void> modifyCustomerLevel(@RequestBody @Validated ModifyCustometLevelReq modifyCustometLevelReq);
}
