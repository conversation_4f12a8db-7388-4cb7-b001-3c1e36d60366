package com.kun.linkage.customer.facade.enums;

public enum CardOperationTypeEnum {
    OPEN_CARD("OPEN_CARD", "开卡"),
    FREEZE_CARD("FREEZE_CARD", "冻结卡"),
    UNFREEZE_CARD("UNFREEZE_CARD", "解冻卡"),
    CANCEL_CARD("CANCEL_CARD", "注销卡"),
    ACTIVATE_CARD("ACTIVATE_CARD", "激活卡");

    private final String type;
    private final String desc;

    CardOperationTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static CardOperationTypeEnum getEnumByType(String type) {
        for (CardOperationTypeEnum organizationBusinessTypeEnum : CardOperationTypeEnum.values()) {
            if (organizationBusinessTypeEnum.getType().equals(type)) {
                return organizationBusinessTypeEnum;
            }
        }
        return null;
    }
}
