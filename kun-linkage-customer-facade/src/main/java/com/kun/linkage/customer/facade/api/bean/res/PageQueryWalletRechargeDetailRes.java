package com.kun.linkage.customer.facade.api.bean.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PageQueryWalletRechargeDetailRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 交易类型(钱包交易流水中的交易类型)
     */
    @Schema(description = "交易类型(钱包交易流水中的交易类型)")
    private String transactionType;
    /**
     * 充值日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "充值日期时间")
    private LocalDateTime rechargeDatetime;
    /**
     * 承兑日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "承兑日期时间")
    private LocalDateTime acceptanceDatetime;
    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    private BigDecimal rechargeAmount;
    /**
     * 充值币种
     */
    @Schema(description = "充值币种")
    private String rechargeCurrencyCode;
    /**
     * 充值币种精度
     */
    @Schema(description = "充值币种精度")
    private Integer rechargeCurrencyPrecision;
    /**
     * 承兑金额
     */
    @Schema(description = "承兑金额")
    private BigDecimal acceptanceAmount;
    /**
     * 承兑币种
     */
    @Schema(description = "承兑币种")
    private String acceptanceCurrencyCode;
    /**
     * 承兑币种
     */
    @Schema(description = "承兑币种精度")
    private Integer acceptanceCurrencyPrecision;
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public LocalDateTime getRechargeDatetime() {
        return rechargeDatetime;
    }

    public void setRechargeDatetime(LocalDateTime rechargeDatetime) {
        this.rechargeDatetime = rechargeDatetime;
    }

    public LocalDateTime getAcceptanceDatetime() {
        return acceptanceDatetime;
    }

    public void setAcceptanceDatetime(LocalDateTime acceptanceDatetime) {
        this.acceptanceDatetime = acceptanceDatetime;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getRechargeCurrencyCode() {
        return rechargeCurrencyCode;
    }

    public void setRechargeCurrencyCode(String rechargeCurrencyCode) {
        this.rechargeCurrencyCode = rechargeCurrencyCode;
    }

    public Integer getRechargeCurrencyPrecision() {
        return rechargeCurrencyPrecision;
    }

    public void setRechargeCurrencyPrecision(Integer rechargeCurrencyPrecision) {
        this.rechargeCurrencyPrecision = rechargeCurrencyPrecision;
    }

    public String getAcceptanceCurrencyCode() {
        return acceptanceCurrencyCode;
    }

    public void setAcceptanceCurrencyCode(String acceptanceCurrencyCode) {
        this.acceptanceCurrencyCode = acceptanceCurrencyCode;
    }

    public BigDecimal getAcceptanceAmount() {
        return acceptanceAmount;
    }

    public void setAcceptanceAmount(BigDecimal acceptanceAmount) {
        this.acceptanceAmount = acceptanceAmount;
    }

    public Integer getAcceptanceCurrencyPrecision() {
        return acceptanceCurrencyPrecision;
    }

    public void setAcceptanceCurrencyPrecision(Integer acceptanceCurrencyPrecision) {
        this.acceptanceCurrencyPrecision = acceptanceCurrencyPrecision;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
