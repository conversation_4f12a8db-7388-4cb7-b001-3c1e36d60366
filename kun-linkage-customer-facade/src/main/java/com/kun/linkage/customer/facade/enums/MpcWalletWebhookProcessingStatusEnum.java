package com.kun.linkage.customer.facade.enums;

public enum MpcWalletWebhookProcessingStatusEnum {
    SUCCESS("SUCCESS", "完成"),
    FAIL("FAIL", "失败"),
    BLOCK("BLOCK", "阻断");

    private final String value;
    private final String desc;

    MpcWalletWebhookProcessingStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static MpcWalletWebhookProcessingStatusEnum getEnumByValue(String value) {
        for (MpcWalletWebhookProcessingStatusEnum mpcWalletWebhookRiskSuggestEnum : MpcWalletWebhookProcessingStatusEnum.values()) {
            if (mpcWalletWebhookRiskSuggestEnum.getValue().equals(value)) {
                return mpcWalletWebhookRiskSuggestEnum;
            }
        }
        return null;
    }
}
