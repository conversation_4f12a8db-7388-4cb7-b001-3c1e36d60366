package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分页查询机构客户账户信息DTO
 */
public class OrganizationCustomerAccountInfoPageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账户号
     */
    @Schema(description = "账户号")
    private String accountNo;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    /**
     * 客户号
     */
    @Schema(description = "客户号")
    private String customerId;

    /**
     * 业务账户类型
     */
    @Schema(description = "业务账户类型")
    private String accountType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;
    /**
     * 最大可用余额
     */
    @Schema(description = "最大可用余额")
    private BigDecimal maxAvailableAmount;
    /**
     * 最小可用余额
     */
    @Schema(description = "最小可用余额")
    private BigDecimal mixAvailableAmount;

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getMaxAvailableAmount() {
        return maxAvailableAmount;
    }

    public void setMaxAvailableAmount(BigDecimal maxAvailableAmount) {
        this.maxAvailableAmount = maxAvailableAmount;
    }

    public BigDecimal getMixAvailableAmount() {
        return mixAvailableAmount;
    }

    public void setMixAvailableAmount(BigDecimal mixAvailableAmount) {
        this.mixAvailableAmount = mixAvailableAmount;
    }
}
