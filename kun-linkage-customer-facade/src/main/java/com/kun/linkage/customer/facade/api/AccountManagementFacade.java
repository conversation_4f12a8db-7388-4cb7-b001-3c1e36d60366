package com.kun.linkage.customer.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.customer.facade.api.bean.req.QueryAccountBalanceReq;
import com.kun.linkage.customer.facade.api.bean.req.QueryBalanceByCardIdReq;
import com.kun.linkage.customer.facade.api.bean.res.QueryAccountBalanceRes;
import com.kun.linkage.customer.facade.api.bean.res.QueryBalanceByCardIdRsp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/api/accountManagement", configuration = FeignConfiguration.class)
public interface AccountManagementFacade {
    /**
     * 根据组织机构号、客户号、币种查询账户余额
     *
     * @param queryAccountBalanceReq
     * @return
     */
    @Operation(description = "根据组织机构号、客户号、币种查询账户余额", summary = "根据组织机构号、客户号、币种查询账户余额")
    @RequestMapping(value = "/queryAccountBalance", method = RequestMethod.POST)
    Result<List<QueryAccountBalanceRes>> queryAccountBalance(@RequestBody @Validated QueryAccountBalanceReq queryAccountBalanceReq);

    @Operation(description = "根据cardId查询账户余额", summary = "根据cardId查询账户余额")
    @RequestMapping(value = "/queryBalanceByCardId", method = RequestMethod.POST)
    Result<QueryBalanceByCardIdRsp> queryBalanceByCardId(@RequestBody @Validated QueryBalanceByCardIdReq queryBalanceByCardIdReq);
}
