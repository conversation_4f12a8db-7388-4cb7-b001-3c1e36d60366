package com.kun.linkage.customer.facade.enums;

public enum WalletOperationTypeEnum {
    CREATE("CREATE", "创建"),
    INVALID("INVALID", "无效");

    private final String value;
    private final String desc;

    WalletOperationTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static WalletOperationTypeEnum getEnumByValue(String value) {
        for (WalletOperationTypeEnum operationTypeEnum : WalletOperationTypeEnum.values()) {
            if (operationTypeEnum.getValue().equals(value)) {
                return operationTypeEnum;
            }
        }
        return null;
    }
}
