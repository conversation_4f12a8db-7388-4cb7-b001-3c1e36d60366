package com.kun.linkage.customer.facade.enums;

public enum CaseStatusEnum {

    NONE("NONE", "无需审核"),
    PENDING("PENDING", "待审核"),
    APPROVED("APPROVED", "通过"),
    REJECTED("REJECTED", "拒绝"),
    SUPPLEMENT("SUPPLEMENT", "待补充");

    private final String code;
    private final String label;

    private final String dictType = "KYC_CASE_STATUS";

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    CaseStatusEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static CaseStatusEnum fromCode(String code) {
        for (CaseStatusEnum status : values()) {
            if (status.code.equalsIgnoreCase(code)) return status;
        }

        return null;
    }
}


