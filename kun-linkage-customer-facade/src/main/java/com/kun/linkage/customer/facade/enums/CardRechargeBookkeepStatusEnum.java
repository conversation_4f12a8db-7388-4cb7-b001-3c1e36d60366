package com.kun.linkage.customer.facade.enums;

public enum CardRechargeBookkeepStatusEnum {
    NOT_BOOKED(0, "未记账或已冲账或记账明确失败"),
    BOOKED(1, "已记账"),
    NO_NEED(2, "无需记账"),
    UNKNOW(3, "未知");

    private final Integer value;
    private final String desc;

    CardRechargeBookkeepStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
