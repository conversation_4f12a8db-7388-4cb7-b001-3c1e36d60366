package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 机构费用模版信息新增提交DTO
 */
public class OrganizationFeeTemplateAddSubmitDTO extends OrganizationFeeTemplateBean {
    /**
     * 模版名称
     */
    @Schema(description = "模版名称")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Length(max = 128, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String templateName;
    /**
     * 状态
     */
    @Schema(description = "状态")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = ValidStatusEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String status;
    /*
    * 机构费用模版明细信息集合
     */
    @Valid
    @Schema(description = "机构费用模版明细信息集合")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private List<OrganizationFeeTemplateDetailAddSubmitDTO> organizationFeeTemplateDetailList;

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<OrganizationFeeTemplateDetailAddSubmitDTO> getOrganizationFeeTemplateDetailList() {
        return organizationFeeTemplateDetailList;
    }

    public void setOrganizationFeeTemplateDetailList(List<OrganizationFeeTemplateDetailAddSubmitDTO> organizationFeeTemplateDetailList) {
        this.organizationFeeTemplateDetailList = organizationFeeTemplateDetailList;
    }
}
