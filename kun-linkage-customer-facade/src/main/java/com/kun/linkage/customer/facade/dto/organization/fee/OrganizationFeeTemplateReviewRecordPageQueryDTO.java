package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分页查询机构费用模版审核信息DTO
 */
public class OrganizationFeeTemplateReviewRecordPageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模版号
     */
    @Schema(description = "模版号")
    private String templateNo;

    /**
     * 审核id
     */
    @Schema(description = "审核id")
    private String reviewId;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String reviewStatus;

    /**
     * 提交开始时间
     */
    @Schema(description = "提交开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitStartTime;

    /**
     * 提交结束时间
     */
    @Schema(description = "提交结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitEndTime;

    /**
     * 审核开始时间
     */
    @Schema(description = "审核开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewStartTime;

    /**
     * 审核结束时间
     */
    @Schema(description = "审核结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewEndTime;

    /**
     * 审核类型
     */
    @Schema(description = "审核类型")
    private String operatorType;

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }

    public String getReviewId() {
        return reviewId;
    }

    public void setReviewId(String reviewId) {
        this.reviewId = reviewId;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public LocalDateTime getSubmitStartTime() {
        return submitStartTime;
    }

    public void setSubmitStartTime(LocalDateTime submitStartTime) {
        this.submitStartTime = submitStartTime;
    }

    public LocalDateTime getSubmitEndTime() {
        return submitEndTime;
    }

    public void setSubmitEndTime(LocalDateTime submitEndTime) {
        this.submitEndTime = submitEndTime;
    }

    public LocalDateTime getReviewStartTime() {
        return reviewStartTime;
    }

    public void setReviewStartTime(LocalDateTime reviewStartTime) {
        this.reviewStartTime = reviewStartTime;
    }

    public LocalDateTime getReviewEndTime() {
        return reviewEndTime;
    }

    public void setReviewEndTime(LocalDateTime reviewEndTime) {
        this.reviewEndTime = reviewEndTime;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
}
