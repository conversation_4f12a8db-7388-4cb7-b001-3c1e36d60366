package com.kun.linkage.customer.facade.enums;

public enum ChainNetworkEnum {
    ETH_ERC20("ETH_ERC20"),
    TRX_TRC20("TRX_TRC20");

    private final String value;

    ChainNetworkEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ChainNetworkEnum getEnumByValue(String value) {
        for (ChainNetworkEnum chainNetworkEnum : ChainNetworkEnum.values()) {
            if (chainNetworkEnum.getValue().equals(value)) {
                return chainNetworkEnum;
            }
        }
        return null;
    }
}
