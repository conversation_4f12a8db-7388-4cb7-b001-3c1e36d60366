package com.kun.linkage.customer.facade.dto.organization.basic;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.customer.facade.enums.OrganizationBusinessTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

/**
 * 添加机构信息提交DTO
 */
public class OrganizationBasicInfoAddSubmitDTO extends OrganizationBasicInfoBean {
    /**
     * 业务类型
     */
    @Schema(description = "业务类型;01:U卡,02:VCC卡")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = OrganizationBusinessTypeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String businessType;

    /**
     * 国家字母码
     */
    @Schema(description = "国家字母码")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String countryCode;

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
}
