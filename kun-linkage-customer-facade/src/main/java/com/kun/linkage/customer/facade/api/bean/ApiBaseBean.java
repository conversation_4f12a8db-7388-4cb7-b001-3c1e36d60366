package com.kun.linkage.customer.facade.api.bean;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

public class ApiBaseBean implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 请求流水号
     */
    @Schema(description = "请求流水号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String requestNo;
    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;
    /**
     * 客户号
     */
    @Schema(description = "客户号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String customerId;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}
