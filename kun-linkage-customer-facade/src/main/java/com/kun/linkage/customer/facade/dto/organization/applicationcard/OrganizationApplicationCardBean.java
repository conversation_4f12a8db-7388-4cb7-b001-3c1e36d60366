package com.kun.linkage.customer.facade.dto.organization.applicationcard;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 机构卡产品信息bean
 */
public class OrganizationApplicationCardBean implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 卡产品
     */
    @Schema(description = "卡产品")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String cardProductCode;

    /**
     * 卡片处理方
     */
    @Schema(description = "卡片处理方")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String processor;

    /**
     * 自动激活标记
     */
    @Schema(description = "自动激活标记;0:No,1:Yes")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Range(max = 1, min = 0, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private Integer autoActivationFlag;

    /**
     * 状态
     */
    @Schema(description = "状态;Valid:有效,Invalid:无效")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = ValidStatusEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String status;

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public Integer getAutoActivationFlag() {
        return autoActivationFlag;
    }

    public void setAutoActivationFlag(Integer autoActivationFlag) {
        this.autoActivationFlag = autoActivationFlag;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
