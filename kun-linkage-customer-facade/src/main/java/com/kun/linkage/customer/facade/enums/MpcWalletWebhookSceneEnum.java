package com.kun.linkage.customer.facade.enums;

public enum MpcWalletWebhookSceneEnum {
    DEPOSIT("DEPOSIT", "充值"),
    WITHDRAW("WITHDRAW", "提现"),
    ENERGY("ENERGY", "补充gas"),
    AGG("AGG", "资金归集");

    private final String value;
    private final String desc;

    MpcWalletWebhookSceneEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static MpcWalletWebhookSceneEnum getEnumByValue(String value) {
        for (MpcWalletWebhookSceneEnum mpcWalletWebhookSceneEnum : MpcWalletWebhookSceneEnum.values()) {
            if (mpcWalletWebhookSceneEnum.getValue().equals(value)) {
                return mpcWalletWebhookSceneEnum;
            }
        }
        return null;
    }

    public static boolean contains(String value) {
        return getEnumByValue(value) != null;
    }
}
