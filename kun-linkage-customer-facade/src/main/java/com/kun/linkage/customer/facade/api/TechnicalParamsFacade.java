package com.kun.linkage.customer.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.customer.facade.dto.TechnicalParamsCreateDTO;
import com.kun.linkage.customer.facade.dto.TechnicalParamsUpdateDTO;
import com.kun.linkage.customer.facade.vo.TechnicalParamsVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 技术参数配置Facade接口
 *
 * @since 2025-07-28
 */
@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/api/technicalParams", configuration = FeignConfiguration.class)
public interface TechnicalParamsFacade {

    /**
     * 根据机构号查询技术参数配置
     *
     * @param organizationNo 机构号
     * @return 技术参数配置
     */
    @Operation(description = "根据机构号查询技术参数配置")
    @RequestMapping(value = "/getByOrganizationNo", method = RequestMethod.GET)
    Result<TechnicalParamsVO> getByOrganizationNo(@RequestParam("organizationNo") String organizationNo);

    /**
     * 新增技术参数配置
     *
     * @param dto 创建数据
     * @return 创建结果
     */
    @Operation(description = "新增技术参数配置")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    Result<TechnicalParamsVO> create(@RequestBody @Validated TechnicalParamsCreateDTO dto);

    /**
     * 更新技术参数配置
     *
     * @param dto 更新数据
     * @return 更新结果
     */
    @Operation(description = "更新技术参数配置")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    Result<Void> update(@RequestBody @Validated TechnicalParamsUpdateDTO dto);
}
