package com.kun.linkage.customer.facade.api.bean.req;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "导出文件记录查询请求对象")
public class ExportFileRecordQueryVO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "文件类型", example = "AUTHORIZATION_EXPORT")
    private String fileType;

    @Schema(description = "文件状态", example = "SUCCESS")
    private String fileStatus;

    @Schema(description = "商户号", example = "123456789")
    private String organizationNo;

    @Schema(description = "创建时间 - 开始", example = "2023-10-01")
    private String createTimeFrom;

    @Schema(description = "创建时间 - 结束", example = "2023-10-31")
    private String createTimeTo;

    // Getters and Setters
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCreateTimeFrom() {
        return createTimeFrom;
    }

    public void setCreateTimeFrom(String createTimeFrom) {
        this.createTimeFrom = createTimeFrom;
    }

    public String getCreateTimeTo() {
        return createTimeTo;
    }

    public void setCreateTimeTo(String createTimeTo) {
        this.createTimeTo = createTimeTo;
    }
}
