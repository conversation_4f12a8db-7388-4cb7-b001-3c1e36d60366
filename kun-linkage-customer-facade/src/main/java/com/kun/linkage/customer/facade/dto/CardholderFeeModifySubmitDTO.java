package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 持卡人费率信息提交DTO
 */
public class CardholderFeeModifySubmitDTO extends CardholderFeeBean {
    /**
     * 费率id
     */
    @Schema(description = "费率id")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Long feeId;

    /*
     * 持卡人费率明细信息集合
     */
    @Valid
    @Schema(description = "持卡人费率明细信息集合")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Size(min = 4, max = 4, message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private List<CardholderFeeDetailModifySubmitDTO> cardholderFeeDetailList;

    public Long getFeeId() {
        return feeId;
    }

    public void setFeeId(Long feeId) {
        this.feeId = feeId;
    }

    public List<CardholderFeeDetailModifySubmitDTO> getCardholderFeeDetailList() {
        return cardholderFeeDetailList;
    }

    public void setCardholderFeeDetailList(List<CardholderFeeDetailModifySubmitDTO> cardholderFeeDetailList) {
        this.cardholderFeeDetailList = cardholderFeeDetailList;
    }
}
