package com.kun.linkage.customer.facade.enums;

public enum WalletTransactionTypeEnum {
    /**
     * 充值
     */
    RECHARGE("RECHARGE", "充值"),
    ;

    private final String value;
    private final String desc;

    WalletTransactionTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static WalletTransactionTypeEnum getEnumByValue(String value) {
        for (WalletTransactionTypeEnum walletTransactionTypeEnum : WalletTransactionTypeEnum.values()) {
            if (walletTransactionTypeEnum.getValue().equals(value)) {
                return walletTransactionTypeEnum;
            }
        }
        return null;
    }
}
