package com.kun.linkage.customer.facade.enums;

public enum OrganizationPoolCurrencyCodeEnum {

    USDT("USDT", 6),
    USD("USD", 2),
    HKD("HKD", 2),
    ;

    private final String value;
    private final Integer precision;
    private final String dictType = "KL_ORGANIZATION_POOL_CCY";

    OrganizationPoolCurrencyCodeEnum(String value, Integer precision) {
        this.value = value;
        this.precision = precision;
    }

    public String getValue() {
        return value;
    }

    public Integer getPrecision() {
        return precision;
    }

    public static OrganizationPoolCurrencyCodeEnum getEnumByValue(String value) {
        for (OrganizationPoolCurrencyCodeEnum itemEnum : OrganizationPoolCurrencyCodeEnum.values()) {
            if (itemEnum.getValue().equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }

    public static Integer getPrecisionByValue(String value) {
        for (OrganizationPoolCurrencyCodeEnum itemEnum : OrganizationPoolCurrencyCodeEnum.values()) {
            if (itemEnum.getValue().equals(value)) {
                return itemEnum.getPrecision();
            }
        }
        return 2;
    }
}
