package com.kun.linkage.customer.facade.enums;

/**
 * 机构费用计费维度枚举
 */
public enum OrganizationFeeBillingDimensionEnum {
    /**
     * 单笔金额
     */
    SINGLE_AMOUNT("SINGLE_AMOUNT", "单笔金额"),
    /**
     * 单笔阶梯金额
     */
    TIERED_SINGLE_AMOUNT("TIERED_SINGLE_AMOUNT", "单笔阶梯金额"),
    ;

    private final String value;
    private final String desc;
    private final String dictType = "KL_ORG_FEE_BILLING_DIMENSION";

    OrganizationFeeBillingDimensionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrganizationFeeBillingDimensionEnum getEnumByValue(String value) {
        for (OrganizationFeeBillingDimensionEnum itemEnum : OrganizationFeeBillingDimensionEnum.values()) {
            if (itemEnum.getValue().equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
}
