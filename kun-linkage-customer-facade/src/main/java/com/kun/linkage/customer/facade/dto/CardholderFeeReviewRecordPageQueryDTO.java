package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分页查询持卡人费率审核信息DTO
 */
public class CardholderFeeReviewRecordPageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    private String organizationNo;

    /**
     * 卡产品编号
     */
    @Schema(description = "卡产品编号")
    private String cardProductCode;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 审核id
     */
    @Schema(description = "审核id")
    private Long reviewId;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String reviewStatus;

    /**
     * 提交开始时间
     */
    @Schema(description = "提交开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitStartTime;

    /**
     * 提交结束时间
     */
    @Schema(description = "提交结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitEndTime;

    /**
     * 审核开始时间
     */
    @Schema(description = "审核开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewStartTime;

    /**
     * 审核结束时间
     */
    @Schema(description = "审核结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewEndTime;

    /**
     * 审核类型
     */
    @Schema(description = "审核类型")
    private String operatorType;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public LocalDateTime getSubmitStartTime() {
        return submitStartTime;
    }

    public void setSubmitStartTime(LocalDateTime submitStartTime) {
        this.submitStartTime = submitStartTime;
    }

    public LocalDateTime getSubmitEndTime() {
        return submitEndTime;
    }

    public void setSubmitEndTime(LocalDateTime submitEndTime) {
        this.submitEndTime = submitEndTime;
    }

    public LocalDateTime getReviewStartTime() {
        return reviewStartTime;
    }

    public void setReviewStartTime(LocalDateTime reviewStartTime) {
        this.reviewStartTime = reviewStartTime;
    }

    public LocalDateTime getReviewEndTime() {
        return reviewEndTime;
    }

    public void setReviewEndTime(LocalDateTime reviewEndTime) {
        this.reviewEndTime = reviewEndTime;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
}
