package com.kun.linkage.customer.facade.enums;

public enum GenderEnum {
    MALE(1, "男"),
    FEMALE(2, "女");

    private final Integer value;

    private final String description;

    private static final String dictType ="GENDER";

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    GenderEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public static GenderEnum getByCode(Integer code) {
        for (GenderEnum gender : GenderEnum.values()) {
            if (gender.getValue().equals(code)) {
                return gender;
            }
        }
        return null; // 或者抛出异常
    }
}
