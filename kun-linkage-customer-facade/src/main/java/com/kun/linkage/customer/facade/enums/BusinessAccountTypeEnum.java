package com.kun.linkage.customer.facade.enums;

public enum BusinessAccountTypeEnum {
    BASIC("001", "法币基本账户"),
    CRYPTO("002","数币基本账户"),
    CREDIT("003","法币信用账户");

    private final String value;
    private final String desc;
    private final String dictType = "KL_BUSINESS_ACCOUNT_TYPE";

    BusinessAccountTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getDictType() {
        return dictType;
    }

    public static BusinessAccountTypeEnum getEnumByValue(String value) {
        for (BusinessAccountTypeEnum accountTypeEnum : BusinessAccountTypeEnum.values()) {
            if (accountTypeEnum.getValue().equals(value)) {
                return accountTypeEnum;
            }
        }
        return null;
    }
}
