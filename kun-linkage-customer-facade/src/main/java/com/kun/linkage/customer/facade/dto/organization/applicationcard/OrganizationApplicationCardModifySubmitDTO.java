package com.kun.linkage.customer.facade.dto.organization.applicationcard;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

/**
 * 机构卡产品信息提交DTO
 */
public class OrganizationApplicationCardModifySubmitDTO extends OrganizationApplicationCardBean {
    /**
     * 机构卡产品ID(修改时需上送)
     */
    @Schema(description = "机构卡产品ID(修改时需上送)")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Long organizationApplicationCardId;

    public Long getOrganizationApplicationCardId() {
        return organizationApplicationCardId;
    }

    public void setOrganizationApplicationCardId(Long organizationApplicationCardId) {
        this.organizationApplicationCardId = organizationApplicationCardId;
    }
}
