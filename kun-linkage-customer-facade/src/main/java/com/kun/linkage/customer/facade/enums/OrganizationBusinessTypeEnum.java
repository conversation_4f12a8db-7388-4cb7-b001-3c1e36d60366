package com.kun.linkage.customer.facade.enums;

public enum OrganizationBusinessTypeEnum {
    U_CARD("01", "U卡"),
    VCC_CARD("02", "VCC卡");

    private final String value;
    private final String desc;
    private final String dictType = "KL_ORGANIZATION_BUSINESS_TYPE";

    OrganizationBusinessTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrganizationBusinessTypeEnum getEnumByValue(String value) {
        for (OrganizationBusinessTypeEnum organizationBusinessTypeEnum : OrganizationBusinessTypeEnum.values()) {
            if (organizationBusinessTypeEnum.getValue().equals(value)) {
                return organizationBusinessTypeEnum;
            }
        }
        return null;
    }
}
