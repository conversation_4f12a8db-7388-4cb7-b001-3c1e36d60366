package com.kun.linkage.customer.facade.enums;

public enum HitResultEnum {

    NONE("NONE", "无"),
    NOT_HIT("NOT_HIT", "未命中"),
    SUSPICIOUS("SUSPICIOUS", "疑似命中");

    private final String value;
    private final String description;

    private final String dictTyp="KYC_HIT_RESULT";


    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    HitResultEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static HitResultEnum fromValue(String value) {
        for (HitResultEnum result : values()) {
            if (result.getValue().equalsIgnoreCase(value))
                return result;
        }
        return null;
    }
}


