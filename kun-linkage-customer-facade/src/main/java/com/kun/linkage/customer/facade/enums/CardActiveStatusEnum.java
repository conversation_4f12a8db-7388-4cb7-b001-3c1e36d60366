package com.kun.linkage.customer.facade.enums;

public enum CardActiveStatusEnum {
    ACTIVATED("ACTIVATED", "已激活"),
    UNACTIVATED("UNACTIVATED", "未激活");

    private final String status;
    private final String desc;

    CardActiveStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static CardActiveStatusEnum getEnumByStatus(String status) {
        for (CardActiveStatusEnum operationStatusEnum : CardActiveStatusEnum.values()) {
            if (operationStatusEnum.getStatus().equals(status)) {
                return operationStatusEnum;
            }
        }
        return null;
    }
}
