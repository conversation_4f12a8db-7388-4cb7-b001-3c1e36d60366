package com.kun.linkage.customer.facade.enums;

public enum IndustryEnum {

    INTERNET_TECH_AI("internet_tech_ai", "互联网/技术服务/AI", "Internet / Technology / AI"),
    DIGITAL_CURRENCY("digital_currency", "支付/数字货币行业", "Digital Currency / Payment Industry"),
    FINANCE("finance", "金融业", "Finance"),
    CONSULTING_SERVICES("consulting_services", "专业咨询服务", "Professional Consulting Services"),
    AGRICULTURE_FORESTRY("agriculture_forestry", "农/林/畜牧/渔业", "Agriculture / Forestry / Animal Husbandry / Fishery"),
    ENERGY_CHEMICAL_MINING("energy_chemical_mining", "能源/化工/采矿/环保", "Energy / Chemical / Mining / Environmental Protection"),
    MANUFACTURING("manufacturing", "制造业", "Manufacturing"),
    REAL_ESTATE_CONSTR("real_estate_constr", "房地产/建筑", "Real Estate / Construction"),
    EDUCATION_SERVICES("education_services", "教育服务", "Education Services"),
    HEALTHCARE("healthcare", "医疗健康", "Healthcare"),
    CONSUMER_GOODS("consumer_goods", "消费品/批发零售业", "Consumer Goods / Wholesale Retail"),
    TOURISM_ACCOMM_FOOD("tourism_accomm_food", "旅游/住宿/餐饮业", "Tourism / Accommodation / Food Services"),
    TRADE_LOGISTICS("trade_logistics", "贸易/物流运输业", "Trade / Logistics / Transportation"),
    ADVERTISING_MEDIA("advertising_media", "广告/传媒/文化/体育", "Advertising / Media / Culture / Sports"),
    GOV_NONPROFIT_OTHER("gov_nonprofit_other", "政府/非盈利机构/其他", "Government / Non-profit Organizations / Other");

    private String value;
    private String chineseDescription;
    private String englishDescription;

    /**
     * 字段的type
     */
    private static final String dictType ="INDUSTRY";

    IndustryEnum(String value, String chineseDescription, String englishDescription) {
        this.value = value;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
    }

    public String getValue() {
        return value;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }
}
