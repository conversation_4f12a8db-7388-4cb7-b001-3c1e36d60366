package com.kun.linkage.customer.facade.dto.organization.basic;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

/**
 * 修改机构信息提交DTO
 */
public class OrganizationBasicInfoModifySubmitDTO extends OrganizationBasicInfoBean {
    /**
     * 机构ID
     */
    @Schema(description = "机构ID")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Long organizationId;

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }
}
