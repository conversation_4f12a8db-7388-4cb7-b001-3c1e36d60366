package com.kun.linkage.customer.facade.enums;

public enum MpcWalletWebhookStatusEnum {
    INIT("INIT", "待上链"),
    PROCESSING("PROCESSING", "进行中"),
    SUCCESS("SUCCESS", "成功"),
    FAILED("FAILED", "失败");

    private final String value;
    private final String desc;

    MpcWalletWebhookStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static MpcWalletWebhookStatusEnum getEnumByValue(String value) {
        for (MpcWalletWebhookStatusEnum mpcWalletWebhookStatusEnum : MpcWalletWebhookStatusEnum.values()) {
            if (mpcWalletWebhookStatusEnum.getValue().equals(value)) {
                return mpcWalletWebhookStatusEnum;
            }
        }
        return null;
    }

    public static boolean contains(String value) {
        return getEnumByValue(value) != null;
    }
}
