package com.kun.linkage.customer.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.api.bean.req.OrgPageQueryCardRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.OrgPageQueryCardRechargeDetailRes;
import com.kun.linkage.customer.facade.dto.OrganizationCustomerCardListQueryDTO;
import com.kun.linkage.customer.facade.vo.OrganizationCustomerCardListQueryVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Org卡管理接口
 *
 * @since 2025-08-01
 */
@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/org/cardManagement", configuration = FeignConfiguration.class)
public interface OrgCardManagementFacade {

    /**
     * 分页查询机构客户账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构客户卡片信息", summary = "分页查询机构客户卡片信息")
    @RequestMapping(value = "/pageList", method = RequestMethod.POST)
    Result<PageResult<OrganizationCustomerCardListQueryVO>> pageList(@RequestBody OrganizationCustomerCardListQueryDTO dto);

    /**
     * 分页查询卡充值记录
     *
     * @param pageQueryCardRechargeDetailReq
     * @return
     */
    @Operation(description = "分页查询卡充值记录", summary = "分页查询卡充值记录")
    @RequestMapping(value = "/pageQueryCardRechargeDetail", method = RequestMethod.POST)
    Result<PageResult<OrgPageQueryCardRechargeDetailRes>> pageQueryCardRechargeDetail(
            @RequestBody @Validated OrgPageQueryCardRechargeDetailReq pageQueryCardRechargeDetailReq);

    /**
     * 异步导出卡充值记录
     *
     * @param req 查询条件
     * @return 导出任务信息
     */
    @Operation(description = "异步导出卡充值记录", summary = "异步导出卡充值记录")
    @RequestMapping(value = "/asyncExportCardRecharge", method = RequestMethod.POST)
    Result<String> asyncExportCardRecharge(@RequestBody @Validated OrgPageQueryCardRechargeDetailReq req);

    }
