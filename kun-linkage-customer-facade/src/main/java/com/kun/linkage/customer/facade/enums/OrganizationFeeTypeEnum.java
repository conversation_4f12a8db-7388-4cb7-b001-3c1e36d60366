package com.kun.linkage.customer.facade.enums;

/**
 * 机构费用类型枚举
 */
public enum OrganizationFeeTypeEnum {
    /**
     * 开卡费
     */
    OPEN_CARD_FEE("01", "开卡费"),
    /**
     * 充值手续费
     */
    RECHARGE_FEE("02", "充值手续费"),
    /**
     * 承兑费
     */
    ACCEPTANCE_FEE("03", "承兑费"),
    /**
     * 交易手续费
     */
    TRANSACTION_FEE("04", "交易手续费"),
    /**
     * 清算手续费
     */
    CLEARING_FEE("05", "清算手续费"),
    /**
     * FX Markup
     */
    FX_MARKUP_FEE("06", "FX Markup"),
    /**
     * 短信手续费
     */
    SMS_FEE("07", "短信手续费"),
    /**
     * 销卡手续费
     */
    CANCEL_CARD_FEE("08", "销卡手续费"),
    ;

    private final String value;
    private final String desc;
    private final String dictType = "KL_ORGANIZATION_FEE_TYPE";

    OrganizationFeeTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrganizationFeeTypeEnum getEnumByValue(String value) {
        for (OrganizationFeeTypeEnum organizationFeeTypeEnum : OrganizationFeeTypeEnum.values()) {
            if (organizationFeeTypeEnum.getValue().equals(value)) {
                return organizationFeeTypeEnum;
            }
        }
        return null;
    }
}
