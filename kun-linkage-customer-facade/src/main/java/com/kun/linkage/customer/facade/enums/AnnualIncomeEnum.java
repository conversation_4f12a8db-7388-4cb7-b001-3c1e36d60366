package com.kun.linkage.customer.facade.enums;

public enum AnnualIncomeEnum {

    UNDER_50K_USD("under_50k_usd", "50000美金以下", "Under 50,000 USD"),
    FIFTY_TO_NINETY_NINE_K("50k_99k_usd", "50000-99999美金", "50,000 - 99,999 USD"),
    HUNDRED_TO_ONE_NINETY_NINE_K("100k_199k_usd", "100000-199999美金", "100,000 - 199,999 USD"),
    TWO_HUNDRED_TO_TWO_NINETY_NINE_K("200k_299k_usd", "200000-299999美金", "200,000 - 299,999 USD"),
    OVER_300K_USD("over_300k_usd", "300000美金以上", "Over 300,000 USD");

    private String value;
    private String chineseDescription;
    private String englishDescription;

    /**
     * 字段的type
     */
    private static final String dictType ="Annual Income";

    AnnualIncomeEnum(String value, String chineseDescription, String englishDescription) {
        this.value = value;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
    }

    public String getValue() {
        return value;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }
}
