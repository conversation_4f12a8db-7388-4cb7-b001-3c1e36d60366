package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 机构费率模版信息提交DTO
 */
public class OrganizationFeeTemplateModifySubmitDTO extends OrganizationFeeTemplateBean {
    /**
     * 模版号
     */
    @Schema(description = "模版号")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String templateNo;

    /*
     * 机构费率模版明细信息集合
     */
    @Valid
    @Schema(description = "机构费率模版明细信息集合")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private List<OrganizationFeeTemplateDetailModifySubmitDTO> organizationFeeTemplateDetailList;

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }

    public List<OrganizationFeeTemplateDetailModifySubmitDTO> getOrganizationFeeTemplateDetailList() {
        return organizationFeeTemplateDetailList;
    }

    public void setOrganizationFeeTemplateDetailList(List<OrganizationFeeTemplateDetailModifySubmitDTO> organizationFeeTemplateDetailList) {
        this.organizationFeeTemplateDetailList = organizationFeeTemplateDetailList;
    }
}
