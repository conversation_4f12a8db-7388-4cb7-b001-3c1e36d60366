package com.kun.linkage.customer.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.api.bean.req.CreateWalletReq;
import com.kun.linkage.customer.facade.api.bean.req.PageQueryWalletRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.CreateWalletRes;
import com.kun.linkage.customer.facade.api.bean.res.PageQueryWalletRechargeDetailRes;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/api/walletManagement", configuration = FeignConfiguration.class)
public interface WalletManagementFacade {

    @Operation(description = "创建钱包")
    @RequestMapping(value = "/createWallet", method = RequestMethod.POST)
    Result<CreateWalletRes> createWallet(@RequestBody @Validated CreateWalletReq createWalletReq);

    @Operation(description = "分页查询钱包充值记录")
    @RequestMapping(value = "/pageQueryWalletRechargeDetail", method = RequestMethod.POST)
    Result<PageResult<PageQueryWalletRechargeDetailRes>> pageQueryWalletRechargeDetail(
            @RequestBody @Validated PageQueryWalletRechargeDetailReq pageQueryWalletRechargeDetailReq);
}
