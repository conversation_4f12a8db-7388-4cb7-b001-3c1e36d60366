package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

public class OrgCustomerAccountProcessBuckChargeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    @Schema(description = "机构客户账号主键id")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationCustomerAccountInfoId;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getOrganizationCustomerAccountInfoId() {
        return organizationCustomerAccountInfoId;
    }

    public void setOrganizationCustomerAccountInfoId(String organizationCustomerAccountInfoId) {
        this.organizationCustomerAccountInfoId = organizationCustomerAccountInfoId;
    }
}
