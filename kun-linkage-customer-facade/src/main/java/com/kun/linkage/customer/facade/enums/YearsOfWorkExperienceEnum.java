package com.kun.linkage.customer.facade.enums;

public enum YearsOfWorkExperienceEnum {

    ZERO_TO_FIVE("0_5_years", "0-5年", "0-5 Years"),
    SIX_TO_TEN("6_10_years", "6-10年", "6-10 Years"),
    ELEVEN_TO_TWENTY("11_20_years", "11-20年", "11-20 Years"),
    OVER_TWENTY("over_20_years", "20年以上", "Over 20 Years");

    private String value;
    private String chineseDescription;
    private String englishDescription;

    /**
     * 字段的type
     */
    private static final String dictType ="Years of Work Experience";

    YearsOfWorkExperienceEnum(String value, String chineseDescription, String englishDescription) {
        this.value = value;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
    }

    public String getValue() {
        return value;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }
}
