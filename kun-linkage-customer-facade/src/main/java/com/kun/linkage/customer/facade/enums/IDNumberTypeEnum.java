package com.kun.linkage.customer.facade.enums;

public enum IDNumberTypeEnum {

    ID_CARD("id_card", "身份证", "ID card","ID_CAR"),
    PASSPORT("passport", "护照", "Passport","PASSPORT");

    private String value;
    private String chineseDescription;
    private String englishDescription;
    private String ocrValue;

    /**
     * 字段的type
     */
    private static final String dictType ="ID_NUMBER_TYPE";

    IDNumberTypeEnum(String value, String chineseDescription, String englishDescription, String ocrValue) {
        this.value = value;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
        this.ocrValue = ocrValue;
    }

    public String getValue() {
        return value;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }

    public String getOcrValue() {
        return ocrValue;
    }

}
