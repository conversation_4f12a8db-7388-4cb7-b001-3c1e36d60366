package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.customer.facade.enums.CaseStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class CustomerKycAuditDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 案件号
     */
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Schema(description = "案件号")
    private String caseNo;

    /**
     * 案件状态 待审核、通过、拒绝、待补充
     */
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Schema(description = "案件状态 待审核、通过、拒绝、待补充")
    private CaseStatusEnum caseStatus;

    /**
     * 审核描述
     */
    @Schema(description = "审核描述")
    private String auditDescription;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public CaseStatusEnum getCaseStatus() {
        return caseStatus;
    }

    public void setCaseStatus(CaseStatusEnum caseStatus) {
        this.caseStatus = caseStatus;
    }

    public String getAuditDescription() {
        return auditDescription;
    }

    public void setAuditDescription(String auditDescription) {
        this.auditDescription = auditDescription;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
