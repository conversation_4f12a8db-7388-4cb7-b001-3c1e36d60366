package com.kun.linkage.customer.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.api.bean.req.OrganizationFeeDetailPageQueryReq;
import com.kun.linkage.customer.facade.api.bean.res.OrganizationFeeDetailRes;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/org/feeDetail", configuration = FeignConfiguration.class)
public interface OrgFeeDetailQueryFacade {
    /**
     * 分页查询机构手续费明细信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(description = "分页查询机构手续费明细信息", summary = "分页查询机构手续费明细信息")
    @RequestMapping(value = "/pageList", method = RequestMethod.POST)
    Result<PageResult<OrganizationFeeDetailRes>> pageList(@RequestBody @Validated OrganizationFeeDetailPageQueryReq req);

    /**
     * 异步导出机构手续费明细信息
     *
     * @param req 查询条件
     * @return 导出任务信息
     */
    @Operation(description = "异步导出机构手续费明细信息", summary = "异步导出机构手续费明细信息")
    @RequestMapping(value = "/asyncExport", method = RequestMethod.POST)
    Result<String> asyncExport(@RequestBody @Validated OrganizationFeeDetailPageQueryReq req);

}
