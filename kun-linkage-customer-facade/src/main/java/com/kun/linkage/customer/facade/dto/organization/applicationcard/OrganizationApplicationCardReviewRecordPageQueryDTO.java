package com.kun.linkage.customer.facade.dto.organization.applicationcard;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 分页查询机构卡产品审核信息DTO
 */
public class OrganizationApplicationCardReviewRecordPageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    private String organizationNo;

    /**
     * 卡产品编号
     */
    @Schema(description = "卡产品编号")
    private String cardProductCode;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String reviewStatus;

    /**
     * 审核类型
     */
    @Schema(description = "审核类型")
    private String operatorType;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
}
