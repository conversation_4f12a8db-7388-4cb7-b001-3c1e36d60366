package com.kun.linkage.customer.facade.enums;

public enum OrganizationModeEnum {
    TRANSFER_TO_THIRD_PARTY_AUTHORIZATION("1", "转三方授权"),
    RECHARGE_POSTING("2", "充值上账");

    private final String value;
    private final String desc;
    private final String dictType = "KL_ORGANIZATION_MODE";

    OrganizationModeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrganizationModeEnum getEnumByValue(String value) {
        for (OrganizationModeEnum organizationModeEnum : OrganizationModeEnum.values()) {
            if (organizationModeEnum.getValue().equals(value)) {
                return organizationModeEnum;
            }
        }
        return null;
    }

    public static boolean contains(String value) {
        return getEnumByValue(value) != null;
    }
}
