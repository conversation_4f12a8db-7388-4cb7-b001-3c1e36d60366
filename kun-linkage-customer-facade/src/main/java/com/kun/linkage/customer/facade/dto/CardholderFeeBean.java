package com.kun.linkage.customer.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 持卡人费率信息bean
 */
public class CardholderFeeBean implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 状态
     */
    @Schema(description = "状态;VALID:有效,INVALID:无效")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = ValidStatusEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String status;

    /**
     * 生效开始时间
     */
    @Schema(description = "生效开始时间:yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private LocalDateTime effectiveStartTime;

    /**
     * 生效结束时间
     */
    @Schema(description = "生效结束时间:yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private LocalDateTime effectiveEndTime;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getEffectiveStartTime() {
        return effectiveStartTime;
    }

    public void setEffectiveStartTime(LocalDateTime effectiveStartTime) {
        this.effectiveStartTime = effectiveStartTime;
    }

    public LocalDateTime getEffectiveEndTime() {
        return effectiveEndTime;
    }

    public void setEffectiveEndTime(LocalDateTime effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
    }
}
