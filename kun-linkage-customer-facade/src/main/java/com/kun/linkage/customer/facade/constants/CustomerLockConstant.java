package com.kun.linkage.customer.facade.constants;

/**
 * 客户服务锁相关常量
 */
public class CustomerLockConstant {
    /**
     * 开卡锁前缀
     */
    public static final String OPEN_CARD_LOCK_PREFIX = "openCardLock:";
    /**
     * 卡冻结解冻锁前缀
     */
    public static final String CARD_FREEZE_UNFREEZE_LOCK_PREFIX = "cardFreezeAndUnfreezeLock:";
    /**
     * 销卡锁前缀
     */
    public static final String CANCEL_CARD_LOCK_PREFIX = "cancelCardLock:";
    /**
     * 开卡状态查询处理锁前缀
     */
    public static final String OPEN_CARD_STATUS_QUERY_LOCK_PREFIX = "openCardStatusQueryLock:";
    /**
     * 卡激活锁前缀
     */
    public static final String CARD_ACTIVATION_LOCK_PREFIX = "cardActivationLock:";
    /**
     * 创建钱包锁前缀
     */
    public static final String CREATE_WALLET_LOCK_PREFIX = "createWalletLock:";
    /**
     * 修改客户信息锁前缀
     */
    public static final String MODIFY_CUSTOMER_INFO_LOCK_PREFIX = "modifyCustomerInfoLock:";
    /**
     * MPC钱包通知事件锁前缀
     */
    public static final String MPC_WALLET_WEBHOOK_LOCK_PREFIX = "mpcWalletWebhookLock:";
    /**
     * 卡充值锁前缀
     */
    public static final String CARD_RECHARGE_LOCK_PREFIX = "cardRechargeLock:";
    /**
     * 卡充值账户冲账锁前缀
     */
    public static final String CARD_RECHARGE_BOOKKEEP_REVERSAL_LOCK_PREFIX = "cardRechargeBookkeepReversalLock:";
    /**
     * 机构费用扣除锁前缀
     */
    public static final String ORGANIZATION_FEE_DEDUCTION_LOCK_PREFIX = "organizationFeeDeductionLock:";
    /**
     * 机构客户账户补扣锁前缀
     */
    public static final String ORG_CUSTOMER_ACCOUNT_PROCESS_BUCK_CHARGE_LOCK_PREFIX = "orgCustomerAccountProcessBuckChargeLock:";
    /**
     * 销卡退余额锁前缀
     */
    public static final String CANCEL_CARD_REFUND_BALANCE_LOCK_PREFIX = "cancelCardRefundBalanceLock:";

}
