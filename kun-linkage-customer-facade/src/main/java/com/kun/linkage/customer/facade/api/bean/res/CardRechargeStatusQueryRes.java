package com.kun.linkage.customer.facade.api.bean.res;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

public class CardRechargeStatusQueryRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 卡充值状态:SUCCESS;FAIL;PENDING;
     */
    @Schema(description = "卡充值状态:SUCCESS;FAIL;PENDING;")
    private String rechargeStatus;
    /**
     * 卡id
     */
    @Schema(description = "卡id")
    private String cardId;
    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    private BigDecimal rechargeAmount;
    /**
     * 充值币种(卡币种)
     */
    @Schema(description = "充值币种(卡币种)")
    private String rechargeCurrencyCode;
    /**
     * 扣除金额(扣除机构的资金)
     */
    @Schema(description = "扣除金额(扣除机构的资金)")
    private BigDecimal deductAmount;
    /**
     * 扣除币种(扣除机构资金的币种)
     */
    @Schema(description = "扣除币种(扣除机构资金的币种)")
    private String deductCurrencyCode;

    public String getRechargeStatus() {
        return rechargeStatus;
    }

    public void setRechargeStatus(String rechargeStatus) {
        this.rechargeStatus = rechargeStatus;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getRechargeCurrencyCode() {
        return rechargeCurrencyCode;
    }

    public void setRechargeCurrencyCode(String rechargeCurrencyCode) {
        this.rechargeCurrencyCode = rechargeCurrencyCode;
    }

    public String getDeductCurrencyCode() {
        return deductCurrencyCode;
    }

    public void setDeductCurrencyCode(String deductCurrencyCode) {
        this.deductCurrencyCode = deductCurrencyCode;
    }

    public BigDecimal getDeductAmount() {
        return deductAmount;
    }

    public void setDeductAmount(BigDecimal deductAmount) {
        this.deductAmount = deductAmount;
    }
}
