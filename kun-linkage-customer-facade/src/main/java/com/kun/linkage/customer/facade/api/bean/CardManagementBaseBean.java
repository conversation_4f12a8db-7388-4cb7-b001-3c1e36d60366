package com.kun.linkage.customer.facade.api.bean;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

public class CardManagementBaseBean extends ApiBaseBean {
    private static final long serialVersionUID = 1L;
    /**
     * 请求ip
     */
    @Schema(description = "请求ip")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String ip;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
