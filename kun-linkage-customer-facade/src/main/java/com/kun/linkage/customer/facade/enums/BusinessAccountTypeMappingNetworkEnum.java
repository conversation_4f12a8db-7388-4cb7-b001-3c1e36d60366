package com.kun.linkage.customer.facade.enums;

public enum BusinessAccountTypeMappingNetworkEnum {
    BASIC(BusinessAccountTypeEnum.BASIC.getValue(), "PAYX", null),
    CRYPTO(BusinessAccountTypeEnum.CRYPTO.getValue(),"KUN", "KUN_MPC"),
    CREDIT(BusinessAccountTypeEnum.CREDIT.getValue(), null, null);

    private final String accountType;
    private final String organizationNetwork;
    private final String customerNetwork;

    BusinessAccountTypeMappingNetworkEnum(String accountType, String organizationNetwork, String customerNetwork) {
        this.accountType = accountType;
        this.organizationNetwork = organizationNetwork;
        this.customerNetwork = customerNetwork;
    }

    public String getAccountType() {
        return accountType;
    }

    public String getOrganizationNetwork() {
        return organizationNetwork;
    }

    public String getCustomerNetwork() {
        return customerNetwork;
    }

    public static String getOrganizationNetworkByAccountType(String accountType) {
        for (BusinessAccountTypeMappingNetworkEnum accountTypeEnum : BusinessAccountTypeMappingNetworkEnum.values()) {
            if (accountTypeEnum.getAccountType().equals(accountType)) {
                return accountTypeEnum.getOrganizationNetwork();
            }
        }
        return null;
    }

    public static String getCustomerNetworkByAccountType(String accountType) {
        for (BusinessAccountTypeMappingNetworkEnum accountTypeEnum : BusinessAccountTypeMappingNetworkEnum.values()) {
            if (accountTypeEnum.getAccountType().equals(accountType)) {
                return accountTypeEnum.getCustomerNetwork();
            }
        }
        return null;
    }
}
