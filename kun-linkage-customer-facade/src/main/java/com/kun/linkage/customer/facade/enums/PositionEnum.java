package com.kun.linkage.customer.facade.enums;

public enum PositionEnum {

    SHAREHOLDER_DIRECTOR("shareholder_director", "股东/董事/合伙人", "Shareholder / Director / Partner"),
    EXECUTIVE("executive", "公司高管", "Company Executive"),
    MANAGER("manager", "部门/项目/区域经理", "Manager (Department/Project/Regional)"),
    SALES_SPECIALIST("sales_specialist", "销售/专员", "Sales / Specialist"),
    ENGINEER_DESIGNER("engineer_designer", "工程师/设计师/医生/教师/律师等专业技术人员", "Engineer / Designer / Doctor / Teacher / Lawyer and other Professionals"),
    ACTOR("actor", "演员", "Actor"),
    INTERN("intern", "实习生", "Intern"),
    FREELANCER("freelancer", "自由职业", "Freelancer");

    private String value;
    private String chineseDescription;
    private String englishDescription;

    /**
     * 字段的type
     */
    private static final String dictType ="Position";

    PositionEnum(String value, String chineseDescription, String englishDescription) {
        this.value = value;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
    }

    public String getValue() {
        return value;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }
}
