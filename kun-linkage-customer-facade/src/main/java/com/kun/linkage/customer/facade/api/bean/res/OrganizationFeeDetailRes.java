package com.kun.linkage.customer.facade.api.bean.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机构手续费明细查询结果VO
 */
@Schema(name = "OrganizationFeeDetailRes", description = "机构手续费明细查询结果")
public class OrganizationFeeDetailRes implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id", example = "1")
    private String id;

    /**
     * 机构号
     */
    @Schema(description = "机构号", example = "ORG001")
    private String organizationNo;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称", example = "测试机构")
    private String organizationName;

    /**
     * 卡产品编码
     */
    @Schema(description = "卡产品编码", example = "CARD001")
    private String cardProductCode;

    /**
     * 手续费计算日期时间
     */
    @Schema(description = "手续费计算日期时间", example = "2025-07-25 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime calculateDatetime;

    /**
     * 交易日期时间
     */
    @Schema(description = "交易日期时间", example = "2025-07-25 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transactionDatetime;

    /**
     * 关联交易id
     */
    @Schema(description = "关联交易id", example = "TXN123456")
    private String relatedTransactionId;

    /**
     * 手续费类型
     */
    @Schema(description = "手续费类型", example = "TRANSACTION_FEE")
    private String feeType;

    /**
     * 手续费收取方式
     */
    @Schema(description = "手续费收取方式", example = "DEDUCT")
    private String feeCollectionMethod;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额", example = "1000.00")
    private BigDecimal transactionAmount;

    /**
     * 交易币种
     */
    @Schema(description = "交易币种", example = "USD")
    private String transactionCurrencyCode;

    /**
     * 交易币种精度
     */
    @Schema(description = "交易币种精度", example = "2")
    private Integer transactionCurrencyPrecision;

    /**
     * 手续费金额(币种同交易币种)
     */
    @Schema(description = "手续费金额", example = "10.00")
    private BigDecimal feeAmount;

    /**
     * 换汇汇率
     */
    @Schema(description = "换汇汇率", example = "1.0000")
    private BigDecimal fxRate;

    /**
     * 扣除处理方(KUN和PAYX)
     */
    @Schema(description = "扣除处理方", example = "KUN")
    private String deductProcessor;

    /**
     * 扣除币种
     */
    @Schema(description = "扣除币种", example = "USD")
    private String deductCurrencyCode;

    /**
     * 扣除币种精度
     */
    @Schema(description = "扣除币种精度", example = "2")
    private Integer deductCurrencyPrecision;

    /**
     * 扣除的费用金额
     */
    @Schema(description = "扣除的费用金额", example = "10.00")
    private BigDecimal deductFeeAmount;

    /**
     * 扣除时的请求流水号
     */
    @Schema(description = "扣除时的请求流水号", example = "REQ123456")
    private String deductRequestNo;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "手续费扣除")
    private String remark;

    /**
     * 手续费收取状态
     */
    @Schema(description = "手续费收取状态(0:未收;1:已收)", example = "1")
    private Integer feeCollectionStatus;

    /**
     * 手续费收取状态描述
     */
    @Schema(description = "手续费收取状态描述", example = "已收")
    private String feeCollectionStatusDesc;

    /**
     * 快照-计费维度
     */
    @Schema(description = "快照-计费维度", example = "AMOUNT")
    private String snapshotBillingDimension;

    /**
     * 快照-金额区间:最小金额
     */
    @Schema(description = "快照-金额区间:最小金额", example = "0.00")
    private BigDecimal snapshotMinAmount;

    /**
     * 快照-金额区间:最大金额
     */
    @Schema(description = "快照-金额区间:最大金额", example = "10000.00")
    private BigDecimal snapshotMaxAmount;

    /**
     * 快照-比例
     */
    @Schema(description = "快照-比例", example = "0.01")
    private BigDecimal snapshotProportionRate;

    /**
     * 快照-比例的保底金额
     */
    @Schema(description = "快照-比例的保底金额", example = "1.00")
    private BigDecimal snapshotProportionMinAmount;

    /**
     * 快照-比例的封顶金额
     */
    @Schema(description = "快照-比例的封顶金额", example = "100.00")
    private BigDecimal snapshotProportionMaxAmount;

    /**
     * 快照-固定值
     */
    @Schema(description = "快照-固定值", example = "5.00")
    private BigDecimal snapshotFixedAmount;

    /**
     * 调用次数
     */
    @Schema(description = "调用次数", example = "1")
    private Integer callCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-07-25 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @Schema(description = "最后一次修改时间", example = "2025-07-25 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifyTime;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public LocalDateTime getCalculateDatetime() {
        return calculateDatetime;
    }

    public void setCalculateDatetime(LocalDateTime calculateDatetime) {
        this.calculateDatetime = calculateDatetime;
    }

    public LocalDateTime getTransactionDatetime() {
        return transactionDatetime;
    }

    public void setTransactionDatetime(LocalDateTime transactionDatetime) {
        this.transactionDatetime = transactionDatetime;
    }

    public String getRelatedTransactionId() {
        return relatedTransactionId;
    }

    public void setRelatedTransactionId(String relatedTransactionId) {
        this.relatedTransactionId = relatedTransactionId;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeCollectionMethod() {
        return feeCollectionMethod;
    }

    public void setFeeCollectionMethod(String feeCollectionMethod) {
        this.feeCollectionMethod = feeCollectionMethod;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public Integer getTransactionCurrencyPrecision() {
        return transactionCurrencyPrecision;
    }

    public void setTransactionCurrencyPrecision(Integer transactionCurrencyPrecision) {
        this.transactionCurrencyPrecision = transactionCurrencyPrecision;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFxRate() {
        return fxRate;
    }

    public void setFxRate(BigDecimal fxRate) {
        this.fxRate = fxRate;
    }

    public String getDeductProcessor() {
        return deductProcessor;
    }

    public void setDeductProcessor(String deductProcessor) {
        this.deductProcessor = deductProcessor;
    }

    public String getDeductCurrencyCode() {
        return deductCurrencyCode;
    }

    public void setDeductCurrencyCode(String deductCurrencyCode) {
        this.deductCurrencyCode = deductCurrencyCode;
    }

    public Integer getDeductCurrencyPrecision() {
        return deductCurrencyPrecision;
    }

    public void setDeductCurrencyPrecision(Integer deductCurrencyPrecision) {
        this.deductCurrencyPrecision = deductCurrencyPrecision;
    }

    public BigDecimal getDeductFeeAmount() {
        return deductFeeAmount;
    }

    public void setDeductFeeAmount(BigDecimal deductFeeAmount) {
        this.deductFeeAmount = deductFeeAmount;
    }

    public String getDeductRequestNo() {
        return deductRequestNo;
    }

    public void setDeductRequestNo(String deductRequestNo) {
        this.deductRequestNo = deductRequestNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getFeeCollectionStatus() {
        return feeCollectionStatus;
    }

    public void setFeeCollectionStatus(Integer feeCollectionStatus) {
        this.feeCollectionStatus = feeCollectionStatus;
    }

    public String getFeeCollectionStatusDesc() {
        return feeCollectionStatusDesc;
    }

    public void setFeeCollectionStatusDesc(String feeCollectionStatusDesc) {
        this.feeCollectionStatusDesc = feeCollectionStatusDesc;
    }

    public String getSnapshotBillingDimension() {
        return snapshotBillingDimension;
    }

    public void setSnapshotBillingDimension(String snapshotBillingDimension) {
        this.snapshotBillingDimension = snapshotBillingDimension;
    }

    public BigDecimal getSnapshotMinAmount() {
        return snapshotMinAmount;
    }

    public void setSnapshotMinAmount(BigDecimal snapshotMinAmount) {
        this.snapshotMinAmount = snapshotMinAmount;
    }

    public BigDecimal getSnapshotMaxAmount() {
        return snapshotMaxAmount;
    }

    public void setSnapshotMaxAmount(BigDecimal snapshotMaxAmount) {
        this.snapshotMaxAmount = snapshotMaxAmount;
    }

    public BigDecimal getSnapshotProportionRate() {
        return snapshotProportionRate;
    }

    public void setSnapshotProportionRate(BigDecimal snapshotProportionRate) {
        this.snapshotProportionRate = snapshotProportionRate;
    }

    public BigDecimal getSnapshotProportionMinAmount() {
        return snapshotProportionMinAmount;
    }

    public void setSnapshotProportionMinAmount(BigDecimal snapshotProportionMinAmount) {
        this.snapshotProportionMinAmount = snapshotProportionMinAmount;
    }

    public BigDecimal getSnapshotProportionMaxAmount() {
        return snapshotProportionMaxAmount;
    }

    public void setSnapshotProportionMaxAmount(BigDecimal snapshotProportionMaxAmount) {
        this.snapshotProportionMaxAmount = snapshotProportionMaxAmount;
    }

    public BigDecimal getSnapshotFixedAmount() {
        return snapshotFixedAmount;
    }

    public void setSnapshotFixedAmount(BigDecimal snapshotFixedAmount) {
        this.snapshotFixedAmount = snapshotFixedAmount;
    }

    public Integer getCallCount() {
        return callCount;
    }

    public void setCallCount(Integer callCount) {
        this.callCount = callCount;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
}
