package com.kun.linkage.customer.facade.enums;

public enum WalletNetworkEnum {
    KUN_MPC("KUN_MPC");

    private final String value;

    WalletNetworkEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static WalletNetworkEnum getEnumByValue(String value) {
        for (WalletNetworkEnum walletNetworkEnum : WalletNetworkEnum.values()) {
            if (walletNetworkEnum.getValue().equals(value)) {
                return walletNetworkEnum;
            }
        }
        return null;
    }
}
