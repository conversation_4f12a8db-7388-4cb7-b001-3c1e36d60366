package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

/**
 * 机构费率配置率信息提交DTO
 */
public class OrganizationFeeConfigModifySubmitDTO extends OrganizationFeeConfigBean {
    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
