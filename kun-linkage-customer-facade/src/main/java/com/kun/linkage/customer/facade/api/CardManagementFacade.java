package com.kun.linkage.customer.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.customer.facade.api.bean.req.*;
import com.kun.linkage.customer.facade.api.bean.res.CardInfoQueryRes;
import com.kun.linkage.customer.facade.api.bean.res.OpenCardRes;
import com.kun.linkage.customer.facade.api.bean.res.OpenCardStatusQueryRes;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/api/cardManagement", configuration = FeignConfiguration.class)
public interface CardManagementFacade {

    @Operation(description = "开卡")
    @RequestMapping(value = "/openCard", method = RequestMethod.POST)
    Result<OpenCardRes> openCard(@RequestBody @Validated OpenCardReq openCardReq);

    @Operation(description = "开卡状态查询")
    @RequestMapping(value = "/openCardStatusQuery", method = RequestMethod.POST)
    Result<OpenCardStatusQueryRes> openCardStatusQuery(@RequestBody @Validated OpenCardStatusQueryReq openCardStatusQueryReq);

    @Operation(description = "卡冻结/解冻")
    @RequestMapping(value = "/cardFreezeAndUnFreezes", method = RequestMethod.POST)
    Result<Void> cardFreezeAndUnFreeze(@RequestBody @Validated CardFreezeAndUnfreezeReq cardFreezeAndUnfreezeReq);

    @Operation(description = "销卡")
    @RequestMapping(value = "/cancelCard", method = RequestMethod.POST)
    Result<Void> cancelCard(@RequestBody @Validated CancelCardReq cancelCardReq);

    @Operation(description = "卡激活")
    @RequestMapping(value = "/cardActivation", method = RequestMethod.POST)
    Result<Void> cardActivation(@RequestBody @Validated CardActivationReq cardActivationReq);

    @Operation(description = "卡信息查询")
    @RequestMapping(value = "/cardInfoQuery", method = RequestMethod.POST)
    Result<CardInfoQueryRes> cardInfoQuery(@RequestBody @Validated CardInfoQueryReq cardInfoQueryReq);
}
