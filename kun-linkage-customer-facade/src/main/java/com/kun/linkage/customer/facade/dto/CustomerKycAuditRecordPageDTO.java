package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

public class CustomerKycAuditRecordPageDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @Schema(description = "客户号")
    private String customerId;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    private String organizationNo;

    /**
     * 案件号
     */
    @Schema(description = "案件号")
    private String caseNo;

    /**
     * 案件类型 申卡前置报送、KYC报送、kyc升级、资料修改
     */
    @Schema(description = "案件类型 申卡前置报送、KYC报送、kyc升级、资料修改")
    private String caseType;

    /**
     * KYC等级 无、零级、 一级、二级、三级
     */
    @Schema(description = "KYC等级 无、零级、 一级、二级、三级")
    private String kycLevel;

    /**
     * 持卡人姓名
     */
    @Schema(description = "持卡人姓名")
    private String cardholderName;

    /**
     * 命中结果 无、未命中、疑似命中
     */
    @Schema(description = "命中结果 无、未命中、疑似命中")
    private String hitResult;

    /**
     * 案件状态 待审核、通过、拒绝、待补充
     */
    @Schema(description = "案件状态 待审核、通过、拒绝、待补充")
    private String caseStatus;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getKycLevel() {
        return kycLevel;
    }

    public void setKycLevel(String kycLevel) {
        this.kycLevel = kycLevel;
    }

    public String getCardholderName() {
        return cardholderName;
    }

    public void setCardholderName(String cardholderName) {
        this.cardholderName = cardholderName;
    }

    public String getHitResult() {
        return hitResult;
    }

    public void setHitResult(String hitResult) {
        this.hitResult = hitResult;
    }

    public String getCaseStatus() {
        return caseStatus;
    }

    public void setCaseStatus(String caseStatus) {
        this.caseStatus = caseStatus;
    }
}
