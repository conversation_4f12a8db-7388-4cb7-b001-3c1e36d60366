package com.kun.linkage.customer.facade.enums;

public enum CryptocurrencyFundingSourceEnum {

    CENTRALIZED_EXCHANGE_ASSET_PROOF("centralized_exchange_asset_proof", "中心化交易所资产证明", "Centralized Exchange Asset Proof"),
    COLD_WALLET("cold_wallet", "去中心化冷钱包", "Cold Wallet"),
    CRYPTO_BANK_STATEMENT("crypto_bank_statement", "加密货币友好银月结单", "Crypto Bank Statement"),
    OTHER("other", "其他", "Other");

    private final String value;
    private final String chineseDescription;
    private final String englishDescription;

    // 构造函数
    CryptocurrencyFundingSourceEnum(String value, String chineseDescription, String englishDescription) {
        this.value = value;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
    }


    public String getValue() {
        return value;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }

    public static CryptocurrencyFundingSourceEnum getByValue(String value) {
        for (CryptocurrencyFundingSourceEnum source : CryptocurrencyFundingSourceEnum.values()) {
            if (source.getValue().equals(value)) {
                return source;
            }
        }
        return null;
    }
}
